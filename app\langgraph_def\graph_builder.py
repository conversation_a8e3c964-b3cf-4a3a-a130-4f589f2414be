    # app/langgraph_def/graph_builder.py
    # -*- coding: utf-8 -*-

    # =================================================================================
    # 1. Imports & Setup (无变动)
    # =================================================================================
    import json
    import sys
    import os
    import re
    import shutil
    import subprocess
    from copy import deepcopy
    from pathlib import Path
    from pprint import pprint
    import time
    import textwrap
    import socket
    from typing import Dict, Optional, List

    from langchain_core.messages import HumanMessage
    from langchain_openai import ChatOpenAI
    from langgraph.graph import StateGraph, END
    from langchain_core.output_parsers import JsonOutputParser
    from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
    from thefuzz import process
    from pydantic import SecretStr

    from .agent_state import AgentState
    from app.models import User, Device
    from openpyxl import Workbook
    from openpyxl.utils import get_column_letter

    # =================================================================================
    # 2. 模型初始化 (修改为使用环境变量)
    # =================================================================================
    API_KEY_STR = os.environ.get('LANGCHAIN_API_KEY')
    BASE_URL = os.environ.get('LANGCHAIN_BASE_URL', "https://ark.cn-beijing.volces.com/api/v3")

    # 检查API_KEY是否设置
    if not API_KEY_STR:
        raise ValueError("LANGCHAIN_API_KEY environment variable is not set. Please set it before running the application.")

    # 转换为SecretStr类型
    API_KEY = SecretStr(API_KEY_STR)

    system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.5, api_key=API_KEY,
                                        base_url=BASE_URL)
    module_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.4, api_key=API_KEY,
                                        base_url=BASE_URL)
    api_designer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.3, api_key=API_KEY, base_url=BASE_URL)
    developer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.2, api_key=API_KEY, base_url=BASE_URL)
    tester_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.1, api_key=API_KEY, base_url=BASE_URL)
    dp_designer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.2, api_key=API_KEY, base_url=BASE_URL)


    # =================================================================================
    # 3. 辅助函数及核心模块生成器 (无变动)
    # =================================================================================

    def robust_rmtree(path: Path, retries: int = 5, delay: int = 1):
        """
        一个更健壮的删除函数，可以处理文件和目录，并在遇到权限错误时重试。
        """
        for i in range(retries):
            if not path.exists():
                return

            try:
                if path.is_dir():
                    shutil.rmtree(path)
                else:
                    path.unlink()

                if not path.exists():
                    return

            except (PermissionError, OSError) as e:
                print(
                    f"  [robust_rmtree] Warn: Attempt {i + 1}/{retries} to delete '{path}' failed: {e}. Retrying in {delay}s...")
                time.sleep(delay)

        if path.exists():
            raise OSError(f"Failed to delete path '{path}' after {retries} retries. It might be locked by another process.")


    API_PACKAGE_DIR = Path(__file__).resolve().parent.parent.parent / 'API_Package'

    def sanitize_filename(name: str) -> str:
        """
        规范化文件名，将中文和特殊字符转换为英文，确保文件名兼容性。

        Args:
            name: 原始名称（可能包含中文）

        Returns:
            str: 规范化后的英文文件名
        """
        # 中文到英文的映射表
        chinese_to_english = {
            '有源蜂鸣器': 'active_buzzer',
            '蜂鸣器': 'buzzer',
            '超声波传感器': 'ultrasonic_sensor',
            '距离传感器': 'distance_sensor',
            '光照传感器': 'light_sensor',
            '温湿度传感器': 'temp_humidity_sensor',
            '温度传感器': 'temperature_sensor',
            '人体红外传感器': 'pir_sensor',
            '运动传感器': 'motion_sensor',
            'LED灯': 'led',
            '继电器': 'relay',
            '显示屏': 'display',
            '驱动器': 'driver',
            '传感器': 'sensor'
        }

        # 首先尝试直接映射
        if name in chinese_to_english:
            return chinese_to_english[name]

        # 如果是已知的型号，直接处理
        if name.upper() in ['HC-SR04', 'HC-SR501', 'BH1750', 'DHT11', 'DHT22', 'DS18B20']:
            return name.lower().replace('-', '').replace(' ', '_')

        # 对于包含中文的复合名称，尝试部分替换
        result = name
        for chinese, english in chinese_to_english.items():
            result = result.replace(chinese, english)

        # 移除或替换特殊字符
        result = result.replace('-', '').replace(' ', '_').replace('(', '').replace(')', '')

        # 如果仍然包含中文字符，使用拼音或通用名称
        if any('\u4e00' <= char <= '\u9fff' for char in result):
            # 包含中文字符，使用通用名称
            if '传感器' in name or 'sensor' in name.lower():
                result = 'generic_sensor'
            elif '蜂鸣器' in name or 'buzzer' in name.lower():
                result = 'buzzer'
            elif 'LED' in name or 'led' in name.lower():
                result = 'led'
            else:
                result = 'generic_device'

        # 特殊处理：如果结果包含已知的传感器类型但还有其他中文，进行二次处理
        if '模块' in result or '传感器' in result:
            result = result.replace('模块', '')
            if not result or any('\u4e00' <= char <= '\u9fff' for char in result):
                if 'temp_humidity_sensor' in result:
                    result = 'temp_humidity_sensor'
                else:
                    result = 'generic_sensor'

        # 确保结果只包含字母、数字和下划线
        result = ''.join(c for c in result if c.isalnum() or c == '_').lower()

        return result if result else 'generic_device'

    def generate_short_names(device_id: str, device_role: str) -> tuple[str, str]:
        """
        生成简短但可读的文件夹名和环境名，避免 Windows 路径长度限制。

        Args:
            device_id: 设备的内部ID
            device_role: 设备角色描述

        Returns:
            tuple: (文件夹名, 环境名)
        """
        project_prefix = "zygo"

        # 提取设备角色的关键词并缩写
        role_keywords = re.findall(r'[a-zA-Z]+', device_role.lower())
        if role_keywords:
            if len(role_keywords) >= 2:
                role_abbrev = role_keywords[0][:2] + role_keywords[1][:2]  # 如 "light sensor" -> "lise"
            else:
                role_abbrev = role_keywords[0][:4]  # 如 "controller" -> "cont"
        else:
            role_abbrev = "dev"  # 默认缩写

        # 取设备ID的后4位作为唯一标识
        device_suffix = device_id[-4:] if len(device_id) >= 4 else device_id

        # 组合成最终的名称
        folder_name = f"{project_prefix}_{role_abbrev}_{device_suffix}"
        env_name = f"{role_abbrev}_{device_suffix}"  # 环境名更短

        return folder_name, env_name

    BOARD_ID_MAP = {
        "esp32": "esp32dev",
        "esp32dev": "esp32dev",
        "esp32-wroom-32": "esp32dev",
        "esp32-wroom-32d": "esp32dev",
        "esp32-wroom-32u": "esp32dev",
        "esp32s3": "esp32-s3-devkitc-1",
        "esp32s2": "esp32-s2-saola-1",
        "esp32c3": "esp32-c3-devkitm-1",
    }


    def find_board_id(user_board_name: str) -> Optional[str]:
        """使用映射表和模糊搜索来查找最匹配的官方开发板ID。"""
        if not user_board_name: return None
        clean_name = user_board_name.strip().lower().replace("-", "").replace("_", "")

        for alias, official_id in BOARD_ID_MAP.items():
            if clean_name == alias.replace("-", "").replace("_", ""):
                print(f"  通过精确匹配找到: '{user_board_name}' -> '{official_id}'")
                return official_id

        best_match, score = process.extractOne(clean_name, BOARD_ID_MAP.keys())
        if score > 85:
            matched_id = BOARD_ID_MAP[best_match]
            print(f"  通过模糊搜索找到匹配: '{user_board_name}' -> '{best_match}' (相似度: {score}%) -> '{matched_id}'")
            return matched_id

        print(f"  在本地映射中未找到 '{user_board_name}' 的高可信度匹配。将使用默认值。")
        return None


    def find_api_spec(peripheral_name: str) -> Optional[str]:
        """
        通过两阶段流程在本地查找外设的API规范：
        1. 精确文件名匹配。
        2. 模糊文件名匹配。
        如果找到，返回文件内容；否则返回 None。
        """
        if not API_PACKAGE_DIR.exists():
            print(f"  警告: API包目录 '{API_PACKAGE_DIR}' 不存在。跳过本地搜索。")
            return None

        exact_filename = f"{peripheral_name.upper().replace(' ', '_')}_API_Package.json"
        exact_filepath = API_PACKAGE_DIR / exact_filename
        if exact_filepath.is_file():
            print(f"  API found via exact match in local cache: '{exact_filename}'")
            try:
                return exact_filepath.read_text(encoding='utf-8')
            except Exception as e:
                print(f"  错误: 读取缓存的API文件失败 {exact_filepath}: {e}")
                return None

        all_api_files = [f.name for f in API_PACKAGE_DIR.glob('*_API_Package.json')]
        if not all_api_files:
            return None

        file_map = {f.replace('_API_Package.json', ''): f for f in all_api_files}
        best_match_core, score = process.extractOne(peripheral_name.upper(), file_map.keys())

        CONFIDENCE_THRESHOLD = 85
        if score >= CONFIDENCE_THRESHOLD:
            fuzzy_filename = file_map[best_match_core]
            fuzzy_filepath = API_PACKAGE_DIR / fuzzy_filename
            print(f"  API found via fuzzy match (Confidence: {score}%): '{peripheral_name}' -> '{fuzzy_filename}'")
            try:
                return fuzzy_filepath.read_text(encoding='utf-8')
            except Exception as e:
                print(f"  错误: 读取缓存的API文件失败 {fuzzy_filepath}: {e}")
                return None

        return None


    def get_local_ip() -> str:
        s = None
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 53))
            ip = s.getsockname()[0]
            return ip
        except Exception as e:
            return "127.0.0.1"
        finally:
            if s: s.close()


    def extract_code(content: str, lang: str = "cpp", block_name: str = None) -> str:
        if block_name:
            pattern = re.compile(rf'\[{block_name.upper()}\]\s*```{lang}\s*([\s\S]*?)\s*```', re.DOTALL)
        else:
            pattern = re.compile(rf'```{lang}\s*([\s\S]*?)\s*```', re.DOTALL)
        match = pattern.search(content)
        return match.group(1).strip() if match else f"// Error: Could not extract code block '{block_name or lang}'."


    def generate_mbedtls_sha256_header() -> str:
        return """
    #ifndef CUSTOM_SHA256_H
    #define CUSTOM_SHA256_H
    #include <Arduino.h>
    #include "mbedtls/sha256.h"
    class SHA256 {
    public:
        SHA256();
        void update(const void *data, size_t len);
        void finalize(byte *hash);
        static String toString(const byte* hash, int len = 32);
    private:
        mbedtls_sha256_context ctx;
    };
    #endif // CUSTOM_SHA256_H
    """


    def generate_mbedtls_sha256_source() -> str:
        return """
    #include "SHA256.h"
    SHA256::SHA256() {
        mbedtls_sha256_init(&ctx);
        mbedtls_sha256_starts_ret(&ctx, 0);
    }
    void SHA256::update(const void *data, size_t len) {
        if (len == 0) return;
        mbedtls_sha256_update_ret(&ctx, (const unsigned char *)data, len);
    }
    void SHA256::finalize(byte *hash) {
        mbedtls_sha256_finish_ret(&ctx, hash);
        mbedtls_sha256_starts_ret(&ctx, 0);
    }
    String SHA256::toString(const byte* hash, int len) {
        char hex_string[len * 2 + 1];
        for (int i = 0; i < len; i++) {
            sprintf(&hex_string[i * 2], "%02x", hash[i]);
        }
        hex_string[len * 2] = '\\0';
        return String(hex_string);
    }
    """


    def generate_tuya_handler_header() -> str:
        return """
    #ifndef TUYA_HANDLER_H
    #define TUYA_HANDLER_H
    #include <Arduino.h>
    #include <WiFiClientSecure.h>
    #include <PubSubClient.h>

    // Define a function pointer type for the application callback
    typedef void (*TuyaAppCallback)(String &topic, String &payload);

    // Provides the calculated credentials for app_main to use in its connection logic
    void tuya_get_mqtt_credentials(char* out_client_id, char* out_username, char* out_password);

    // A non-blocking, passive initialization function
    void tuya_init(WiFiClientSecure& wifiClient, PubSubClient& mqttClient, TuyaAppCallback app_callback);

    // A function to be called by app_main AFTER a successful connection
    void tuya_subscribe_topics();

    void tuya_loop();

    // Publishes data to the Tuya cloud
    bool tuya_publish_data(const String& data_json_string);

    // A function for app_main's master callback to dispatch Tuya messages
    void tuya_handle_mqtt_message(char *topic, byte *payload, unsigned int length);

    #endif // TUYA_HANDLER_H
    """


    def generate_config_manager_header(
        device_id: str,
        wifi_ssid: str,
        wifi_password: str,
        product_id: str,
        cloud_device_id: str,
        device_secret: str
    ) -> str:
        local_pc_ip = get_local_ip()

        # 使用传入的参数，如果为空则使用占位符
        final_wifi_ssid = wifi_ssid if wifi_ssid else "YOUR_WIFI_SSID"
        final_wifi_password = wifi_password if wifi_password else "YOUR_WIFI_PASSWORD"
        final_product_id = product_id if product_id else "YOUR_PRODUCT_ID"
        final_cloud_device_id = cloud_device_id if cloud_device_id else "YOUR_DEVICE_ID"
        final_device_secret = device_secret if device_secret else "YOUR_DEVICE_SECRET"

        print("--- [generate_config_manager_header] Generating with following data: ---")
        print(f"  WIFI_SSID: {final_wifi_ssid}")
        print(f"  WIFI_PASSWORD: {'*' * len(final_wifi_password) if final_wifi_password else '(empty)'}")
        print(f"  TUYA_PRODUCT_ID: {final_product_id}")
        print(f"  TUYA_DEVICE_ID: {final_cloud_device_id}")
        print(f"  TUYA_DEVICE_SECRET: {'*' * len(final_device_secret) if final_device_secret else '(empty)'}")
        print("---------------------------------------------------------------------")

        return f'''
    #ifndef CONFIG_MANAGER_H
    #define CONFIG_MANAGER_H

    // --- Wi-Fi Credentials ---
    #define WIFI_SSID "{final_wifi_ssid}"
    #define WIFI_PASSWORD "{final_wifi_password}"

    // --- Tuya Cloud Credentials ---
    #define TUYA_PRODUCT_ID "{final_product_id}"
    #define TUYA_DEVICE_ID  "{final_cloud_device_id}"
    #define TUYA_DEVICE_SECRET "{final_device_secret}"

    // --- Local Network Configuration ---
    #define MQTT_BROKER "{local_pc_ip}"
    #define MQTT_PORT 1883
    #define OTA_HTTP_SERVER "{local_pc_ip}"
    #define OTA_HTTP_PORT 8000

    // --- Device Identity ---
    #define DEVICE_ID "{device_id}"
    #define FIRMWARE_VERSION "1.0.0"

    // --- MQTT Topics ---
    #define OTA_TOPIC_BASE "/ota/"
    #define DEBUG_TOPIC_BASE "/debug/"

    #endif // CONFIG_MANAGER_H
    '''


    def generate_tuya_handler_source() -> str:
        # V2 Refactored: This handler is now a passive library.
        # It provides credentials but does not manage the connection itself.
        # FIX: Corrected the formatting of the multi-line CA certificate string to resolve C++ compilation errors.
        return """
    #include "tuya_handler.h"
    #include "config_manager.h"
    #include <ArduinoJson.h>
    #include <time.h>
    #include "SHA256.h"

    // The root CA certificate for Tuya's MQTT broker
    static const char tuya_ca_cert[] PROGMEM =
        "-----BEGIN CERTIFICATE-----\\n"
        "MIIGiTCCBXGgAwIBAgIIGkbwkRaiCBgwDQYJKoZIhvcNAQELBQAwgbQxCzAJBgNV\\n"
        "BAYTAlVTMRAwDgYDVQQIEwdBcml6b25hMRMwEQYDVQQHEwpTY290dHNkYWxlMRow\\n"
        "GAYDVQQKExFHb0RhZGR5LmNvbSwgSW5jLjEtMCsGA1UECxMkaHR0cDovL2NlcnRz\\n"
        "LmdvZGFkZHkuY29tL3JlcG9zaXRvcnkvMTMwMQYDVQQDEypHbyBEYWRkeSBTZWN1\\n"
        "cmUgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IC0gRzIwHhcNMjQwODE5MDU1NTM4WhcN\\n"
        "MjUwOTIwMDU1NTM4WjAXMRUwEwYDVQQDDAwqLnR1eWFjbi5jb20wggEiMA0GCSqG\\n"
        "SIb3DQEBAQUAA4IBDwAwggEKAoIBAQDlYq+PYMUih5G0Ob9XX1a57li+CA2YCy38\\n"
        "1gmpStB+/XqC4mHc8GYhEV9rnfd0egs8R6G9J/FwXw0UfNER3UEg1WYEJ0Hi6eMX\\n"
        "0BI65+wZdvJxEpFhwcXU50tPTADxudw8I5haJ5Cv453yH7/kg2M2Qk32YjLwV9Yz\\n"
        "79c6Ogzsg27FCDTghiWuqMq3cImNcYGKC0vNv5D8B+YjI41n1a0hgZXloP9478b/\\n"
        "S/uxPZdg8CpsXRpcTwmxcOScy7ip4aqiYjvjwVB2ZIhprTJiwdInWfiUYitgD5j+\\n"
        "JzW1hahMwu3fgYYozcduYdbbTyicAbGN88EvT1XChTEun72Tw0JfAgMBAAGjggM5\\n"
        "MIIDNTAMBgNVHRMBAf8EAjAAMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcD\\n"
        "AjAOBgNVHQ8BAf8EBAMCBaAwOQYDVR0fBDIwMDAuoCygKoYoaHR0cDovL2NybC5n\\n"
        "b2RhZGR5LmNvbS9nZGlnMnMxLTI4ODM0LmNybDBdBgNVHSAEVjBUMEgGC2CGSAGG\\n"
        "/W0BBxcBMDkwNwYIKwYBBQUHAgEWK2h0dHA6Ly9jZXJ0aWZpY2F0ZXMuZ29kYWRk\\n"
        "eS5jb20vcmVwb3NpdG9yeS8wCAYGZ4EMAQIBMHYGCCsGAQUFBwEBBGowaDAkBggr\\n"
        "BgEFBQcwAYYYaHR0cDovL29jc3AuZ29kYWRkeS5jb20vMEAGCCsGAQUFBzAChjRo\\n"
        "dHRwOi8vY2VydGlmaWNhdGVzLmdvZGFkZHkuY29tL3JlcG9zaXRvcnkvZ2RpZzIu\\n"
        "Y3J0MB8GA1UdIwQYMBaAFEDCvSeOzDSDMKIz1/tss/C0LIDOMCMGA1UdEQQcMBqC\\n"
        "DCoudHV5YWNuLmNvbYIKdHV5YWNuLmNvbTAdBgNVHQ4EFgQUGwrpXqEzmkB903gf\\n"

        "iQpZ18e8geMwggF9BgorBgEEAdZ5AgQCBIIBbQSCAWkBZwB2ABLxTjS9U3JMhAYZ\\n"
        "w48/ehP457Vih4icbTAFhOvlhiY6AAABkWk0kgkAAAQDAEcwRQIgfAhWc0NWPQFk\\n"
        "KdCfg4A9A+Io0bWQdAKr6/vYpr3IQaYCIQCbYHiULR0Nkw4cGtFK3HympmuNbgkt\\n"
        "rd51XUQcTfCwTAB1AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAAB\\n"
        "kWk0ktwAAAQDAEYwRAIgKM9rEIVMjHCUnxUkQYgXeVvVume85E6oiHoFfBaGuIEC\\n"
        "IER4giiSxqR4ftNJkfi8v4ftQrrOt7iZ4FDlnSzKBCpLAHYAzPsPaoVxCWX+lZtT\\n"
        "zumyfCLphVwNl422qX5UwP5MDbAAAAGRaTSTmwAABAMARzBFAiBCqgaBigm1c/hH\\n"
        "owy25qZfn+I8mpc+H1VrVlEZZqAj8gIhAI2ZV/CaarGQ/j8HRwHq7vO+5j/QlXbJ\\n"
        "tI/XHIHqZQW2MA0GCSqGSIb3DQEBCwUAA4IBAQAnWV1if9nZK6aVftzj/w2VkmBY\\n"
        "zBLSO+3Co1Qyc3qxBsCpdLxVCycN9HcmOAAgVdMg5WLs542KGMvIahh0PJzyIrMG\\n"
        "uTeLUvUmb9yGZb+oDLlsqLeAxJZi/Mf4ZN5Ezq52bDotXb6+qrftCrQj+Vz3dp9N\\n"
        "U9XGvts/lM1dpnnoCoVpMTM+kzyzkmIJbb/zSy8U1TLbja5HYdtYVodeMexG+PE/\\n"
        "F+OGeB3AWU5yhSr65XRMWKynNglfspsnvU2azab+3CViOFsCR6Th30ohQKgxjldQ\\n"
        "xfN2SPdPvZjOnmXZT75rMeGahN8PqloYFP12VwsF+IPo3m50U2hstS2IiA3U\\n"
        "-----END CERTIFICATE-----\\n"
        "-----BEGIN CERTIFICATE-----\\n"
        "MIIE0DCCA7igAwIBAgIBBzANBgkqhkiG9w0BAQsFADCBgzELMAkGA1UEBhMCVVMx\\n"
        "EDAOBgNVBAgTB0FyaXpvbmExEzARBgNVBAcTClNjb3R0c2RhbGUxGjAYBgNVBAoT\\n"
        "EUdvRGFkZHkuY29tLCBJbmMuMTEwLwYDVQQDEyhHbyBEYWRkeSBSb290IENlcnRp\\n"
        "ZmljYXRlIEF1dGhvcml0eSAtIEcyMB4XDTExMDUwMzA3MDAwMFoXDTMxMDUwMzA3\\n"
        "MDAwMFowgbQxCzAJBgNVBAYTAlVTMRAwDgYDVQQIEwdBcml6b25hMRMwEQYDVQQH\\n"
        "EwpTY290dHNkYWxlMRowGAYDVQQKExFHb0RhZGR5LmNvbSwgSW5jLjEtMCsGA1UE\\n"
        "CxMkaHR0cDovL2NlcnRzLmdvZGFkZHkuY29tL3JlcG9zaXRvcnkvMTMwMQYDVQQD\\n"
        "EypHbyBEYWRkeSBTZWN1cmUgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IC0gRzIwggEi\\n"
        "MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC54MsQ1K92vdSTYuswZLiBCGzD\\n"
        "BNliF44v/z5lz4/OYuY8UhzaFkVLVat4a2ODYpDOD2lsmcgaFItMzEUz6ojcnqOv\\n"
        "K/6AYZ15V8TPLvQ/MDxdR/yaFrzDN5ZBUY4RS1T4KL7QjL7wMDge87Am+GZHY23e\\n"
        "cSZHjzhHU9FGHbTj3ADqRay9vHHZqm8A29vNMDp5T19MR/gd71vCxJ1gO7GyQ5HY\\n"
        "pDNO6rPWJ0+tJYqlxvTV0KaudAVkV4i1RFXULSo6Pvi4vekyCgKUZMQWOlDxSq7n\\n"
        "eTOvDCAHf+jfBDnCaQJsY1L6d8EbyHSHyLmTGFBUNUtpTrw700kuH9zB0lL7AgMB\\n"
        "AAGjggEaMIIBFjAPBgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBBjAdBgNV\\n"
        "HQ4EFgQUQMK9J47MNIMwojPX+2yz8LQsgM4wHwYDVR0jBBgwFoAUOpqFBxBnKLbv\\n"
        "9r0FQW4gwZTaD94wNAYIKwYBBQUHAQEEKDAmMCQGCCsGAQUFBzABhhhodHRwOi8v\\n"
        "b2NzcC5nb2RhZGR5LmNvbS8wNQYDVR0fBC4wLDAqoCigJoYkaHR0cDovL2NybC5n\\n"
        "b2RhZGR5LmNvbS9nZHJvb3QtZzIuY3JsMEYGA1UdIAQ/MD0wOwYEVR0gADAzMDEG\\n"

        "CCsGAQUFBwIBFiVodHRwczovL2NlcnRzLmdvZGFkZHkuY29tL3JlcG9zaXRvcnkv\\n"
        "MA0GCSqGSIb3DQEBCwUAA4IBAQAIfmyTEMg4uJapkEv/oV9PBO9sPpyIBslQj6Zz\\n"
        "91cxG7685C/b+LrTW+C05+Z5Yg4MotdqY3MxtfWoSKQ7CC2iXZDXtHwlTxFWMMS2\\n"
        "RJ17LJ3lXubvDGGqv+QqG+6EnriDfcFDzkSnE3ANkR/0yBOtg2DZ2HKocyQetawi\\n"
        "DsoXiWJYRBuriSUBAA/NxBti21G00w9RKpv0vHP8ds42pM3Z2Czqrpv1KrKQ0U11\\n"
        "GIo/ikGQI31bS/6kA1ibRrLDYGCD+H1QQc7CoZDDu+8CL9IVVO5EFdkKrqeKM+2x\\n"
        "LXY2JtwE65/3YR8V3Idv7kaWKK2hJn0KCacuBKONvPi8BDAB\\n"
        "-----END CERTIFICATE-----\\n"
        "-----BEGIN CERTIFICATE-----\\n"
        "MIIEfTCCA2WgAwIBAgIDG+cVMA0GCSqGSIb3DQEBCwUAMGMxCzAJBgNVBAYTAlVT\\n"
        "MSEwHwYDVQQKExhUaGUgR28gRGFkZHkgR3JvdXAsIEluYy4xMTAvBgNVBAsTKEdv\\n"
        "IERhZGR5IENsYXNzIDIgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTQwMTAx\\n"
        "MDcwMDAwWhcNMzEwNTMwMDcwMDAwWjCBgzELMAkGA1UEBhMCVVMxEDAOBgNVBAgT\\n"
        "B0FyaXpvbmExEzARBgNVBAcTClNjb3R0c2RhbGUxGjAYBgNVBAoTEUdvRGFkZHku\\n"
        "Y29tLCBJbmMuMTEwLwYDVQQDEyhHbyBEYWRkeSBSb290IENlcnRpZmljYXRlIEF1\\n"
        "dGhvcml0eSAtIEcyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv3Fi\\n"
        "CPH6WTT3G8kYo/eASVjpIoMTpsUgQwE7hPHmhUmfJ+r2hBtOoLTbcJjHMgGxBT4H\\n"
        "Tu70+k8vWTAi56sZVmvigAf88xZ1gDlRe+X5NbZ0TqmNghPktj+pA4P6or6KFWp/\\n"
        "3gvDthkUBcrqw6gElDtGfDIN8wBmIsiNaW02jBEYt9OyHGC0OPoCjM7T3UYH3go+\\n"
        "6118yHz7sCtTpJJiaVElBWEaRIGMLKlDliPfrDqBmg4pxRyp6V0etp6eMAo5zvGI\\n"
        "gPtLXcwy7IViQyU0AlYnAZG0O3AqP26x6JyIAX2f1PnbU21gnb8s51iruF9G/M7E\\n"
        "GwM8CetJMVxpRrPgRwIDAQABo4IBFzCCARMwDwYDVR0TAQH/BAUwAwEB/zAOBgNV\\n"
        "HQ8BAf8EBAMCAQYwHQYDVR0OBBYEFDqahQcQZyi27/a9BUFuIMGU2g/eMB8GA1Ud\\n"
        "IwQYMBaAFNLEsNKR1EwRcbNhyz2h/t2oatTjMDQGCCsGAQUFBwEBBCgwJjAkBggr\\n"
        "BgEFBQcwAYYYaHR0cDovL29jc3AuZ29kYWRkeS5jb20vMDIGA1UdHwQrMCkwJ6Al\\n"
        "oCOGIWh0dHA6Ly9jcmwuZ29kYWRkeS5jb20vZ2Ryb290LmNybDBGBgNVHSAEPzA9\\n"
        "MDsGBFUdIAAwMzAxBggrBgEFBQcCARYlaHR0cHM6Ly9jZXJ0cy5nb2RhZGR5LmNv\\n"
        "bS9yZXBvc2l0b3J5LzANBgkqhkiG9w0BAQsFAAOCAQEAWQtTvZKGEacke+1bMc8d\\n"
        "H2xwxbhuvk679r6XUOEwf7ooXGKUwuN+M/f7QnaF25UcjCJYdQkMiGVnOQoWCcWg\\n"
        "OJekxSOTP7QYpgEGRJHjp2kntFolfzq3Ms3dhP8qOCkzpN1nsoX+oYggHFCJyNwq\\n"
        "9kIDN0zmiN/VryTyscPfzLXs4Jlet0lUIDyUGAzHHFIYSaRt4bNYC8nY7NmuHDKO\\n"
        "KHAN4v6mF56ED71XcLNa6R+ghlO773z/aQvgSMO3kwvIClTErF0UZzdsyqUvMQg3\\n"
        "qm5vjLyb4lddJIGvl5echK1srDdMZvNhkREg5L4wn3qkKQmw4TRfZHcYQFHfjDCm\\n"
        "rw==\\n"
        "-----END CERTIFICATE-----\\n";

    static PubSubClient* _mqttClient;
    static TuyaAppCallback _app_callback = nullptr;

    static String hmac256(const char* key, size_t key_len, const char* message, size_t msg_len) {
        SHA256 sha;
        byte k_ipad[64], k_opad[64];
        memset(k_ipad, 0, sizeof(k_ipad));
        memset(k_opad, 0, sizeof(k_opad));
        memcpy(k_ipad, key, key_len);
        memcpy(k_opad, key, key_len);
        for (int i = 0; i < 64; i++) {
            k_ipad[i] ^= 0x36;
            k_opad[i] ^= 0x5c;
        }
        sha.update(k_ipad, sizeof(k_ipad));
        sha.update(message, msg_len);
        byte hmac[32];
        sha.finalize(hmac);
        sha.update(k_opad, sizeof(k_opad));
        sha.update(hmac, sizeof(hmac));
        sha.finalize(hmac);
        return SHA256::toString(hmac);
    }

    // This function is now EXPOSED to app_main to get credentials
    void tuya_get_mqtt_credentials(char* out_client_id, char* out_username, char* out_password) {
        long int t = time(NULL);
        sprintf(out_client_id, "tuyalink_%s", TUYA_DEVICE_ID);
        sprintf(out_username, "%s|signMethod=hmacSha256,timestamp=%ld,secureMode=1,accessType=1", TUYA_DEVICE_ID, t);
        String sign_content = String("deviceId=") + TUYA_DEVICE_ID + ",timestamp=" + t + ",secureMode=1,accessType=1";
        String pass_hash = hmac256(TUYA_DEVICE_SECRET, strlen(TUYA_DEVICE_SECRET), sign_content.c_str(), sign_content.length());
        strcpy(out_password, pass_hash.c_str());
    }

    // The new "init" function, which is non-blocking and passive.
    void tuya_init(WiFiClientSecure& wifiClient, PubSubClient& mqttClient, TuyaAppCallback app_callback) {
        _mqttClient = &mqttClient;
        _app_callback = app_callback;

        // Configure the WiFiClientSecure with the necessary CA certificate
        wifiClient.setCACert(tuya_ca_cert);

        // The main app will set the server and port before connecting
    }

    void tuya_subscribe_topics() {
        if (_mqttClient && _mqttClient->connected()) {
            char topic_sub[128];
            sprintf(topic_sub, "tylink/%s/thing/property/set", TUYA_DEVICE_ID);
            _mqttClient->subscribe(topic_sub);
        }
    }

    void tuya_loop() {
        // The main app is responsible for the MQTT loop. This function does nothing.
    }

    bool tuya_publish_data(const String& data_json_string) {
        if (!_mqttClient || !_mqttClient->connected()) {
            return false;
        }
        char topic[128];
        sprintf(topic, "tylink/%s/thing/property/report", TUYA_DEVICE_ID);
        return _mqttClient->publish(topic, data_json_string.c_str());
    }

    // The internal callback that dispatches messages to the user-defined app callback
    void tuya_handle_mqtt_message(char *topic, byte *payload, unsigned int length) {
        String topicStr(topic);
        // Check if this message is a Tuya message
        if (topicStr.indexOf(String("tylink/") + TUYA_DEVICE_ID) != -1) {
            String payloadStr;
            for (unsigned int i = 0; i < length; i++) {
                payloadStr += (char)payload[i];
            }
            if (_app_callback) {
                _app_callback(topicStr, payloadStr);
            }
        }
    }
    """


    def generate_ota_handler_header() -> str:
        return """
    #ifndef OTA_HANDLER_H
    #define OTA_HANDLER_H
    #include <WiFi.h>
    #include <PubSubClient.h>
    #include <HTTPUpdate.h>

    void ota_init(WiFiClient& wifiClient, PubSubClient& mqttClient, HTTPUpdate& httpUpdateClient);
    void ota_loop();
    const char* ota_get_device_id();

    // [最终修正] 将接口升级为使用安全的String对象
    void ota_handle_mqtt_message(const String& topic, const String& payload);

    #endif // OTA_HANDLER_H
    """


    # 文件: app/langgraph_def/graph_builder.py

    def generate_ota_handler_source() -> str:
        return """
    #include <WiFi.h>
    #include <HTTPUpdate.h>
    #include <PubSubClient.h>
    #include <ArduinoJson.h>
    #include "config_manager.h"
    #include "ota_handler.h"
    #include "mqtt_logger.h"

    static WiFiClient* _wifiClient;
    static PubSubClient* _mqttClient;
    static HTTPUpdate* _httpUpdateClient;
    static String status_topic, specific_cmd_topic, broadcast_cmd_topic;

    static void perform_ota(String fileName) {
        logger.println("====== [OTA DIAGNOSIS] Step 4: perform_ota() entered. ======");
        logger.printf("Attempting OTA for file: /%s\\n", fileName.c_str());
        logger.printf("Target Server: %s:%d\\n", OTA_HTTP_SERVER, OTA_HTTP_PORT);
        if (!_wifiClient || !_httpUpdateClient) {
            logger.println("FATAL: WiFiClient or HTTPUpdateClient pointer is NULL before update!");
            return;
        }
        WiFiClient updateClient;
        logger.println("Calling httpUpdate.update()... This is a blocking call.");
        t_httpUpdate_return ret = _httpUpdateClient->update(updateClient, OTA_HTTP_SERVER, OTA_HTTP_PORT, "/" + fileName);
        logger.printf("httpUpdate.update() returned with code: %d\\n", ret);
        JsonDocument doc;
        char buffer[256];
        switch (ret) {
            case HTTP_UPDATE_FAILED:
                logger.printf("OTA RESULT: FAILED. Error (%d): %s\\n", _httpUpdateClient->getLastError(), _httpUpdateClient->getLastErrorString().c_str());
                doc["status"] = "failed";
                doc["error_code"] = _httpUpdateClient->getLastError();
                doc["error_message"] = _httpUpdateClient->getLastErrorString();
                serializeJson(doc, buffer);
                _mqttClient->publish(status_topic.c_str(), buffer);
                break;
            case HTTP_UPDATE_NO_UPDATES:
                logger.println("OTA RESULT: NO UPDATES.");
                doc["status"] = "no_update";
                serializeJson(doc, buffer);
                _mqttClient->publish(status_topic.c_str(), buffer);
                break;
            case HTTP_UPDATE_OK:
                logger.println("OTA RESULT: SUCCESS. Device will reboot.");
                doc["status"] = "success";
                serializeJson(doc, buffer);
                _mqttClient->publish(status_topic.c_str(), buffer);
                break;
        }
        logger.println("====== [OTA DIAGNOSIS] Step 5: perform_ota() finished. ======");
    }

    void ota_init(WiFiClient& wifiClient, PubSubClient& mqttClient, HTTPUpdate& httpUpdateClient) {
        _wifiClient = &wifiClient;
        _mqttClient = &mqttClient;
        _httpUpdateClient = &httpUpdateClient;
        status_topic = String(OTA_TOPIC_BASE) + DEVICE_ID + "/status";
        specific_cmd_topic = String(OTA_TOPIC_BASE) + DEVICE_ID + "/command";
        broadcast_cmd_topic = String(OTA_TOPIC_BASE) + "all/command";

        // [DIAGNOSTIC LOG] 确认ota_init被调用，并打印出它将要监听的主题
        logger.println("====== [OTA DIAGNOSIS] Step 0: ota_init() called. ======");
        logger.printf(" - OTA command topic set to: %s\\n", specific_cmd_topic.c_str());

        if (_mqttClient->connected()) {
            _mqttClient->subscribe(specific_cmd_topic.c_str());
            _mqttClient->subscribe(broadcast_cmd_topic.c_str());
        }
    }

    void ota_loop() {}
    const char* ota_get_device_id() { return DEVICE_ID; }

    // [最终修正] 更新函数定义，并使用安全的String对象进行操作
        void ota_handle_mqtt_message(const String& topic, const String& payload) {
            logger.println("====== [OTA DIAGNOSIS] Step 2: ota_handle_mqtt_message() entered. ======");
            
            logger.printf(" - Incoming topic: %s\\n", topic.c_str());
            logger.printf(" - Expected topic: %s\\n", specific_cmd_topic.c_str());

            if (topic != specific_cmd_topic && topic != broadcast_cmd_topic) {
                logger.println(" - Verdict: Topic MISMATCH. Exiting handler.");
                return; 
            }

            logger.println(" - Verdict: Topic MATCH. Proceeding to parse payload.");
            
            JsonDocument doc;
            DeserializationError error = deserializeJson(doc, payload);
            if (error) { 
                logger.printf("OTA JSON parsing failed: %s\\n", error.c_str()); 
                return; 
            }

            const char* action = doc["action"];
            if (action && strcmp(action, "update") == 0) {
                logger.println("====== [OTA DIAGNOSIS] Step 3: Action 'update' confirmed. ======");
                String fileName = doc["file"] | "";
                if (fileName.length() > 0) { 
                    perform_ota(fileName); 
                } else {
                    logger.println("ERROR: Action was 'update' but file name was missing!");
                }
            }
        }
    """

    def generate_mqtt_logger_header() -> str:
        """
        V3 - 生成 MqttLogger 的头文件 (Singleton Implementation) - 已修正
        """
        return """
    #ifndef MQTT_LOGGER_H
    #define MQTT_LOGGER_H
    #include <PubSubClient.h>
    #include <Print.h>

    class MqttLogger : public Print {
    public:
        // 获取单例实例
        static MqttLogger& getInstance();

        // 使用MQTT客户端和设备ID初始化日志记录器
        void begin(PubSubClient& client, const char* device_id);

        // 在主循环中调用此函数以处理缓冲区刷新
        void loop();

        // Print 接口方法
        virtual size_t write(uint8_t);
        virtual size_t write(const uint8_t *buffer, size_t size);

    private:
        // 私有构造函数以强制执行单例模式
        MqttLogger();

        // 私有析构函数
        ~MqttLogger();

        // 删除拷贝构造函数和赋值运算符
        MqttLogger(const MqttLogger&) = delete;
        MqttLogger& operator=(const MqttLogger&) = delete;

        PubSubClient* _client;
        String _topic;
        char _buffer[256];
        size_t _buffer_pos;
        unsigned long _last_flush;
        bool _initialized;

        void flush();
    };

    // V3.2 修正: 添加 extern 声明，让所有包含此头文件的文件都知道 logger 的存在。
    // 它的实体将在 app_main.ino 中被定义。
    extern Print& logger;

    #endif // MQTT_LOGGER_H
    """

    def generate_mqtt_logger_source() -> str:
        """
        V3 - 生成 MqttLogger 的源文件 (Singleton Implementation)
        """
        return """
    #include "mqtt_logger.h"
    #include "config_manager.h" // For DEBUG_TOPIC_BASE

    // 唯一的静态实例
    MqttLogger& MqttLogger::getInstance() {
        static MqttLogger instance;
        return instance;
    }

    // 私有构造函数
    MqttLogger::MqttLogger()
        : _client(nullptr), _buffer_pos(0), _last_flush(0), _initialized(false) {}

    // 私有析构函数
    MqttLogger::~MqttLogger() {}

    // 初始化方法
    void MqttLogger::begin(PubSubClient& client, const char* device_id) {
        _client = &client;
        _topic = String(DEBUG_TOPIC_BASE) + device_id + "/log";
        _initialized = true;
    }

    void MqttLogger::loop() {
        if (_initialized && _buffer_pos > 0 && (millis() - _last_flush > 1000)) {
            flush();
        }
    }

    size_t MqttLogger::write(uint8_t c) {
        if (!_initialized) return 0;
        if (_buffer_pos >= sizeof(_buffer) - 1) {
            flush();
        }
        _buffer[_buffer_pos++] = c;
        if (c == '\\n') { // 在换行时刷新以获得更好的响应性
            flush();
        }
        return 1;
    }

    size_t MqttLogger::write(const uint8_t *buffer, size_t size) {
        if (!_initialized) return 0;
        for (size_t i = 0; i < size; i++) {
            write(buffer[i]);
        }
        return size;
    }

    void MqttLogger::flush() {
        if (_initialized && _buffer_pos > 0 && _client && _client->connected()) {
            _buffer[_buffer_pos] = '\\0';
            _client->publish(_topic.c_str(), _buffer);
            _buffer_pos = 0;
        }
        _last_flush = millis();
    }
    """

    # =================================================================================
    # 4. Agent Node Definitions (新增契约调试器节点)
    # =================================================================================

    def contract_debugger_node(state: AgentState) -> Dict:
        """
        一个简单的调试节点，用于在关键步骤打印所有可用的契约信息。
        """
        print("\n" + "="*25 + " [CONTRACT DEBUGGER] " + "="*25)
        current_device = state.get('current_device_task', {}).get('device_role', 'N/A')
        print(f"DEVICE CONTEXT: '{current_device}'")

        # 打印统一通信契约
        unified_contract = state.get('unified_communication_contract')
        if unified_contract:
            print("\n--- ✅ [Unified Communication Contract] ---")
            pprint(unified_contract)
        else:
            print("\n--- ❌ [Unified Communication Contract]: NOT FOUND ---")

        # 打印当前设备的DP契约
        device_dp_contract = state.get('device_dp_contract')
        if device_dp_contract:
            print("\n--- ✅ [Current Device DP Contract] ---")
            pprint(device_dp_contract)
        else:
            print("\n--- ❌ [Current Device DP Contract]: NOT FOUND ---")

        # 打印当前设备的任务契约
        task_contract = state.get('current_device_task', {}).get('task_contract')
        if task_contract:
            print("\n--- ✅ [Current Device Task Contract] ---")
            pprint(task_contract)
        else:
            print("\n--- ❌ [Current Device Task Contract]: NOT FOUND ---")

        print("="*70 + "\n")
        # 这个节点只打印信息，不修改状态
        return {}

    def module_architect_node(state: AgentState) -> Dict:
        # 导入智能日志函数
        from app.services.workflow_service import _log_with_personality

        device_task = state['current_device_task']
        device_id = device_task['internal_device_id']
        device_role = device_task.get('device_role', '设备')
        workflow_id = state.get('workflow_id')

        _log_with_personality(workflow_id, f"现在我来设计{device_role}的整体架构...分析需要哪些核心模块", "planning", delay=2.0)
        print(f"--- L2: MODULE ARCHITECT: Designing firmware for '{device_id}' ---")

        peripherals_info_parts = []
        for p in device_task.get('peripherals', []):
            model_str = f" (Model: {p.get('model', 'N/A')})"
            pins_str = ""
            pins = p.get('pins', [])
            if not pins and 'pin' in p:
                pins = [{'name': 'PIN', 'number': p['pin']}]

            if pins:
                pin_details = ", ".join([f"{pin.get('name', 'PIN').upper()}: {pin.get('number', 'Not specified')}" for pin in pins])
                pins_str = f" (Pins: {pin_details})"

            peripherals_info_parts.append(f"- {p['name']}{model_str}{pins_str}")
        peripherals_info = "\n".join(peripherals_info_parts)

        prompt = textwrap.dedent("""
        <Prompt>
            <Role>You are an expert embedded firmware architect.</Role>
            <Goal>For the given device, design a modular firmware architecture that includes all necessary functionalities.</Goal>
            <Context>
                <Device>{} ({})</Device>
                <DeviceRole>{}</DeviceRole>
                <Peripherals>
                {}""".format(device_id, device_task['board'], device_task['description'], peripherals_info) + """
                </Peripherals>
            </Context>
            <Instructions>
            1.  **Driver Modules**: For each physical peripheral, define a 'driver' module. The `task_id` should be based on the peripheral's Model or Name, ending with `_driver`.
            2.  **CRITICAL: File Naming Rules**: All `task_id` values MUST use ONLY English characters, numbers, and underscores. NO Chinese characters are allowed in file names. Examples:
            - "有源蜂鸣器" → "active_buzzer_driver"
            - "HC-SR04" → "hcsr04_driver"
            - "超声波传感器" → "ultrasonic_sensor_driver"
            - "蜂鸣器" → "buzzer_driver"
            3.  **Application Module**: Define one single 'application' module named `app_main` that uses all other modules.
            3.  **Core Services (Mandatory)**: You MUST ALWAYS include these three core service modules: `config_manager`, `ota_handler`, and `mqtt_logger`. They are essential for any network-connected device.
            4.  **Cloud Service (Conditional)**: If the device's description mentions any of these cloud-related keywords: "Tuya", "涂鸦", "涂鸦云", "云平台", "cloud platform", "Tuya cloud", you MUST ADDITIONALLY include the `tuya_handler` module. This is CRITICAL for cloud connectivity.
            5.  **Dependencies**: The `app_main` module MUST list all other generated modules as its dependencies.
            6.  **Output Format**: Your final output MUST be a single, valid JSON object containing one key: "modules".
            7.  **No Peripherals Rule**: If a device has no physical peripherals listed in its `<Peripherals>` context, you MUST NOT generate any hardware-specific driver modules for it (e.g., no `light_sensor_driver`). The only modules should be the mandatory Core Services, an optional Cloud Service, and the Application module.
            8.  **Analog Sensor Exception**: If a peripheral's model is 'Generic Analog Sensor', you MUST NOT create a separate driver module for it. Its logic is simple and should be handled directly in `app_main`. Therefore, do not add it to the "modules" list and do not list it as a dependency for `app_main`.
            </instructions>
            <ExampleOutput>
            ```json
            {
                "modules": [
                    { "task_id": "config_manager", "task_type": "driver", "peripheral": "Core", "description": "Manages all network and device configurations.", "dependencies": [] },
                    { "task_id": "ota_handler", "task_type": "driver", "peripheral": "Core", "description": "Handles over-the-air firmware updates.", "dependencies": [] },
                    { "task_id": "mqtt_logger", "task_type": "driver", "peripheral": "Core", "description": "Handles remote logging over MQTT for debugging and verification.", "dependencies": [] },
                    { "task_id": "tuya_handler", "task_type": "driver", "peripheral": "Core", "description": "Handles connection and data exchange with the Tuya Cloud.", "dependencies": [] },
                    { "task_id": "hcsr04_driver", "task_type": "driver", "peripheral": "HC-SR04", "description": "A driver for the HC-SR04 ultrasonic distance sensor.", "dependencies": [] },
                    { "task_id": "active_buzzer_driver", "task_type": "driver", "peripheral": "有源蜂鸣器", "description": "A driver for the active buzzer.", "dependencies": [] },
                    { "task_id": "app_main", "task_type": "application", "description": "The main application logic.", "dependencies": ["config_manager", "ota_handler", "mqtt_logger", "tuya_handler", "hcsr04_driver", "active_buzzer_driver"] }
                ]
            }
            ```
            </ExampleOutput>
        </Prompt>
        """)
        response = module_architect_model.invoke([HumanMessage(content=prompt)])
        try:
            resp_text = str(response.content).strip()
            # 优先尝试从代码块提取
            json_text = extract_code(resp_text, "json")
            if not json_text:
                # 如果失败，回退到正则搜索
                print("   -> Fallback: No JSON code block found. Searching for raw JSON object.")
                m = re.search(r'\{[\s\S]*\}', resp_text)
                json_text = m.group(0) if m else ""

            if not json_text:
                raise ValueError("No JSON content found in the response.")

            plan = json.loads(json_text)

            # 【修复】确保重要的状态被保留，特别是 unified_communication_contract
            result = {"module_tasks": plan['modules'], "original_module_plan": plan['modules']}

            # 保留关键的全局状态
            if 'unified_communication_contract' in state:
                result['unified_communication_contract'] = state['unified_communication_contract']
                print(f"  -> ✅ Preserved unified_communication_contract in module_architect_node")

            return result
        except (json.JSONDecodeError, KeyError, TypeError, ValueError) as e:
            print(f"MODULE ARCHITECT PARSING ERROR: {e}")
            print(f"LLM Raw Response was:\n---\n{response.content}\n---")
            return {"feedback": f"FAIL: Module Architect failed. Error: {e}", "module_tasks": []}


    def generate_task_contract(device_role: str, peripherals: list, topic_map: dict) -> dict:
        """
        生成设备任务契约，明确定义数据源、允许/禁止的硬件API等
        """
        role_lower = device_role.lower()

        # 判断数据源类型
        data_source = "local"  # 默认为本地采集
        has_sensors = any(p.get('name', '').lower() in ['bh1750', 'ds1624', 'apds9960', 'dht22', 'hc-sr04', '超声波传感器', '距离传感器']
                        or p.get('model', '').lower() in ['hc-sr04', 'bh1750', 'ds1624', 'apds9960', 'dht22']
                        for p in peripherals)

        # 如果设备角色包含"报警"、"alarm"等关键词，且没有传感器外设，则为远程数据源
        alarm_keywords = ['报警', 'alarm', '警报', 'alert', '通知', 'notify']
        is_alarm_device = any(keyword in role_lower for keyword in alarm_keywords)

        if is_alarm_device and not has_sensors:
            data_source = "remote"

        # 如果设备只有订阅任务，没有发布任务，也可能是远程数据源
        device_comm = topic_map.get(device_role, {"pub": [], "sub": []})
        if device_comm["sub"] and not device_comm["pub"]:
            data_source = "remote"

        # 生成核心逻辑摘要
        if data_source == "remote":
            core_logic = f"Subscribe to MQTT topics, parse received data, and execute control logic based on the data values."
        else:
            sensor_names = [p.get('name', '') for p in peripherals if p.get('name')]
            if sensor_names:
                core_logic = f"Read data from local sensors ({', '.join(sensor_names)}), process the values, and publish to MQTT topics."
            else:
                core_logic = "Execute device-specific control logic and communicate via MQTT."

        # 定义允许和禁止的硬件API
        allowed_apis = []
        forbidden_apis = []

        if data_source == "local" and has_sensors:
            # 本地采集设备，允许调用传感器API
            for peripheral in peripherals:
                sensor_name = peripheral.get('name', '').lower()
                if sensor_name == 'bh1750':
                    allowed_apis.extend(['bh1750_init', 'bh1750_read_lux'])
                elif sensor_name == 'ds1624':
                    allowed_apis.extend(['ds1624_init', 'ds1624_read_temperature'])
                elif sensor_name == 'apds9960':
                    allowed_apis.extend(['apds9960_init', 'apds9960_read_ambient_light'])
                elif sensor_name == 'dht22':
                    allowed_apis.extend(['dht22_init', 'dht22_read'])

            # 如果有模拟传感器，允许analogRead
            analog_sensors = [p for p in peripherals if p.get('type') == 'analog']
            if analog_sensors:
                allowed_apis.append('analogRead')
        else:
            # 远程数据源设备，禁止所有传感器读取API
            forbidden_apis.extend([
                'analogRead', 'bh1750_read_lux', 'ds1624_read_temperature',
                'apds9960_read_ambient_light', 'dht22_read'
            ])

        return {
            "data_source": data_source,
            "core_logic_summary": core_logic,
            "allowed_hardware_apis": allowed_apis,
            "forbidden_hardware_apis": forbidden_apis
        }

    def plan_enrichment_node(state: AgentState) -> Dict:
        """
        [V4 - Contract-Enhanced]: An AI node that refines user-friendly requirements into machine-friendly instructions
        and generates structured task contracts to eliminate semantic ambiguity.
        """
        print("--- [PLAN ENRICHMENT V4]: Generating task contracts and refining device descriptions... ---")

        project_name = state.get('project_name', 'default_project')
        device_tasks = state.get('device_tasks_queue', [])
        communication_plan = state.get('system_plan', {}).get('communication', [])

        if not device_tasks:
            return {}

        # 1. Create communication map with structured topics
        topic_map = {}
        # Sanitize project name for use in topic - 使用更合理的默认值
        safe_project_name = re.sub(r'[^a-zA-Z0-9_-]', '', project_name.lower().replace(' ', '_'))
        if not safe_project_name:  # 如果项目名被完全过滤掉（如中文项目名），使用默认值
            safe_project_name = "smart_system"

        for comm in communication_plan:
            source_role = comm.get('source_device_role')
            target_role = comm.get('target_device_role')

            safe_source_role = re.sub(r'[^a-zA-Z0-9_-]', '', source_role.lower().replace(' ', '_'))
            if not safe_source_role:  # 如果设备角色被完全过滤掉（如中文角色名），使用映射
                role_mapping = {
                    '光照采集端': 'light_collector',
                    '报警器': 'alarm_device',
                    '温度传感器': 'temp_sensor',
                    '湿度传感器': 'humidity_sensor'
                }
                safe_source_role = role_mapping.get(source_role, 'device')

            topic = f"/{safe_project_name}/{safe_source_role}/data"

            # 🔧 DEBUG: 确保生成的主题不是占位符
            print(f"  -> Generated topic for '{source_role}': '{topic}'")

            if source_role not in topic_map: topic_map[source_role] = {"pub": [], "sub": []}
            if target_role not in topic_map: topic_map[target_role] = {"pub": [], "sub": []}

            if topic not in topic_map[source_role]["pub"]:
                topic_map[source_role]["pub"].append(topic)
            if topic not in topic_map[target_role]["sub"]:
                topic_map[target_role]["sub"].append(topic)

        # 2. Generate task contracts and rewrite descriptions for each device
        for task in device_tasks:
            role = task.get('device_role')
            original_description = task.get('description', '')
            peripherals = task.get('peripherals', [])

            # Generate task contract
            task_contract = generate_task_contract(role, peripherals, topic_map)
            task['task_contract'] = task_contract

            print(f"  -> Generated task contract for '{role}':")
            print(f"     Data Source: {task_contract['data_source']}")
            print(f"     Allowed APIs: {task_contract['allowed_hardware_apis']}")
            print(f"     Forbidden APIs: {task_contract['forbidden_hardware_apis']}")

            comm_context = "This device has no assigned inter-device communication tasks."
            if role in topic_map:
                pub_info = f"It MUST publish data to the following MQTT topics: {', '.join(topic_map[role]['pub'])}." if \
                topic_map[role]["pub"] else ""
                sub_info = f"It MUST subscribe to the following MQTT topics to receive data: {', '.join(topic_map[role]['sub'])}." if \
                topic_map[role]["sub"] else ""
                comm_context = " ".join(filter(None, [pub_info, sub_info]))

            # Add contract-based constraints to the rewrite prompt
            contract_constraints = ""
            if task_contract['data_source'] == 'remote':
                contract_constraints = f"""
                <ContractConstraints>
                CRITICAL: This device MUST NOT perform any local sensor reading. All data must come from subscribed MQTT messages only.
                The device is configured as a data consumer, not a data producer.
                Forbidden APIs: {', '.join(task_contract['forbidden_hardware_apis'])}
                </ContractConstraints>
                """
            elif task_contract['allowed_hardware_apis']:
                contract_constraints = f"""
                <ContractConstraints>
                This device is configured for local data collection.
                Allowed sensor APIs: {', '.join(task_contract['allowed_hardware_apis'])}
                </ContractConstraints>
                """

            rewrite_prompt = textwrap.dedent(f"""
            <Prompt>
                <Role>You are a technical writer specializing in embedded systems. Your task is to rewrite a high-level functional description into a precise, unambiguous technical specification in English for a developer.</Role>
                <Goal>Combine the device's core function with its specific communication tasks and contract constraints into a single, clear paragraph in English.</Goal>
                <Context>
                    <DeviceRole>{role}</DeviceRole>
                    <OriginalDescription>{original_description}</OriginalDescription>
                    <TechnicalCommunicationPlan>{comm_context}</TechnicalCommunicationPlan>
                    {contract_constraints}
                </Context>
                <Instructions>
                    1.  Translate the core function from the `<OriginalDescription>` into clear, concise English.
                    2.  Integrate the specific actions from the `<TechnicalCommunicationPlan>`. Replace vague phrases like "send data to another device" with explicit technical actions like "publish data to the MQTT topic ...".
                    3.  STRICTLY follow the `<ContractConstraints>` if provided. If the device is marked as remote data source, emphasize that it must NOT read local sensors.
                    4.  The final output MUST be a single, concise paragraph in English.
                    5.  Do not add any new functionality. Your job is to translate and specify, not invent.
                </Instructions>
                <Example>
                    <Input>
                        <OriginalDescription>监听光照数据，当光照强度低于阈值时触发报警</OriginalDescription>
                        <TechnicalCommunicationPlan>It MUST subscribe to the following MQTT topics to receive data: /smart_light_system/light_sensor/data.</TechnicalCommunicationPlan>
                        <ContractConstraints>
                        CRITICAL: This device MUST NOT perform any local sensor reading. All data must come from subscribed MQTT messages only.
                        </ContractConstraints>
                    </Input>
                    <Output>
                    Subscribe to MQTT topic /smart_light_system/light_sensor/data to receive light intensity values from remote sensors. Parse the received JSON data to extract illumination levels. When the light intensity falls below a predefined threshold, trigger alarm notifications via Tuya Cloud or local actuators. This device operates as a data consumer and must not perform any local sensor readings.
                    </Output>
                </Example>
                <FinalOutput>
                {{Your rewritten English description here as a single string.}}
                </FinalOutput>
            </Prompt>
            """)

            response = system_architect_model.invoke([HumanMessage(content=rewrite_prompt)])
            new_description = response.content.strip()

            task['description'] = new_description
            print(f"  -> Rewrote description for '{role}':")
            print(f"     [Before]: {original_description}")
            print(f"     [After]:  {task['description']}")

        # 3. 建立统一的通信契约作为唯一真相来源
        unified_communication_contract = {
            "topic_map": topic_map,
            "project_name": safe_project_name
        }

        print(f"  -> Established unified communication contract:")
        for role, topics in topic_map.items():
            if topics["pub"]:
                print(f"     {role} publishes to: {topics['pub']}")
            if topics["sub"]:
                print(f"     {role} subscribes to: {topics['sub']}")

        # 调试：确认我们即将返回的数据
        result = {
            "device_tasks_queue": device_tasks,
            "unified_communication_contract": unified_communication_contract
        }

        print(f"  -> 🔍 DEBUG: plan_enrichment_node returning unified_communication_contract: {unified_communication_contract is not None}")
        if unified_communication_contract:
            print(f"  -> 🔍 DEBUG: Contract contains {len(unified_communication_contract.get('topic_map', {}))} device roles")

        return result

    def device_dispatcher_node(state: AgentState) -> Dict:
        """
        【V2.0 已修复】状态门控节点。
        为每个设备创建一个全新的、纯净的上下文，仅传递必要的全局状态。
        这可以从根本上解决跨设备状态污染的问题。
        """
        queue = state.get('device_tasks_queue', [])

        # 检查是否所有设备都已处理完毕
        if not queue:
            print("--- [DEVICE DISPATCHER] All device tasks completed. ---")
            return {"current_device_task": None}

        # 准备处理下一个设备
        next_device_task = queue[0]
        remaining_queue = queue[1:]
        print(f"\n--- [DEVICE DISPATCHER] Preparing for next device: '{next_device_task.get('device_role')}' ---")

        # --- 核心修正：采用"白名单"模式，而不是"黑名单"清理模式 ---
        # 1. 创建一个全新的、干净的状态字典用于下一个设备循环
        new_state_for_next_device = {}

        # 2. 显式地、逐一地将必须跨设备传递的"全局状态"复制过去
        #    这是唯一真相来源，确保 contract 不会丢失
        global_keys_to_preserve = [
            "user_input",
            "system_plan",
            "user_id",
            "workflow_id",
            "workspace_path",
            "project_name",
            "wifi_ssid",
            "wifi_password",
            "cloud_product_id",
            "cloud_device_id",
            "cloud_device_secret",
            "unified_communication_contract"  # <-- 确保这个最重要的契约被传递
        ]

        print("  -> Preserving global state for next loop:")
        for key in global_keys_to_preserve:
            if key in state:
                new_state_for_next_device[key] = state[key]
                # 调试：确认 unified_communication_contract 的内容
                if key == "unified_communication_contract":
                    contract = state[key]
                    print(f"    - ✅ {key}: Present and passed.")
                    if contract and isinstance(contract, dict):
                        topic_map = contract.get('topic_map', {})
                        print(f"      -> Topic map contains {len(topic_map)} device roles: {list(topic_map.keys())}")
                    else:
                        print(f"      -> ⚠️ WARNING: Contract exists but is not a valid dict: {type(contract)}")
                else:
                    print(f"    - {key}: Passed.")
            else:
                print(f"    - ❌ {key}: NOT FOUND in current state")

        # 3. 注入新的任务信息
        new_state_for_next_device["device_tasks_queue"] = remaining_queue
        new_state_for_next_device["current_device_task"] = next_device_task

        # 4. 重置UI步骤状态
        current_steps = state.get('workflow_steps', [])
        if current_steps:
            dev_loop_steps = [
                "module_architect", "module_dispatcher", "api_designer", "developer",
                "integrator", "test_plan_designer", "deployment_and_verification",
                "compile_node", "pre_deployment_pause", "usb_upload_node",
                "ota_deployment_node", "deploy_and_verify_node", "dp_designer"
            ]
            for step in current_steps:
                if step['id'] in dev_loop_steps:
                    step['status'] = 'pending'
                    # 保留历史日志，不清空 step['log']
                    step['start_time'] = 0.0
                    step['end_time'] = 0.0
                    step['output'] = None
            new_state_for_next_device["workflow_steps"] = current_steps

        # 5. 从数据库加载新设备所需的信息
        user = User.query.get(state['user_id'])
        device = Device.query.filter_by(internal_device_id=next_device_task['internal_device_id']).first()

        # 填充数据库信息到新状态中
        if user:
            print(f"  -> Found User '{user.username}'. Loading WiFi credentials.")
            new_state_for_next_device['wifi_ssid'] = user.wifi_ssid
            new_state_for_next_device['wifi_password'] = user.wifi_password

        if device:
            print(f"  -> Found Device '{device.nickname}'. Loading Tuya credentials.")
            new_state_for_next_device['cloud_product_id'] = device.cloud_product_id
            new_state_for_next_device['cloud_device_id'] = device.cloud_device_id
            new_state_for_next_device['cloud_device_secret'] = device.cloud_device_secret

        # 6. 任何未在上面白名单中列出的键（如 feedback, completed_modules, test_plan 等）
        #    都会被自动丢弃，从而实现了完美的、无副作用的状态隔离。
        print("  -> All temporary states from the previous device have been discarded.")

        return new_state_for_next_device


    def module_dispatcher_node(state: AgentState) -> Dict:
        module_tasks = state.get('module_tasks', [])
        current_task = state.get('current_module_task')
        feedback = state.get('feedback', '')

        print(f"--- L3 DISPATCHER: Module tasks queue length: {len(module_tasks)} ---")

        # 🔧 CRITICAL FIX: 处理契约违规导致的重新生成情况
        if current_task and "FAIL" in feedback and "Contract violations detected" in feedback:
            print(f"--- L3 DISPATCHER: Contract violation detected, regenerating module '{current_task['task_id']}' ---")
            print(f"  -> 🔄 Keeping current task for regeneration: {current_task['task_id']}")

            # V2.0 CONTRACT-FIRST: 调试输出
            if current_task['task_id'] == 'app_main':
                print(f"  -> 🎯 Regenerating app_main task! Dependencies: {current_task.get('dependencies', [])}")
                dp_contract = state.get('device_dp_contract', [])
                print(f"  -> 📋 DP contract available: {len(dp_contract)} items")

            # 保持当前任务不变，清除失败反馈，让开发者重新生成
            return {"current_module_task": current_task, "module_tasks": module_tasks, "feedback": ""}

        if module_tasks:
            next_task = module_tasks[0]
            print(f"--- L3 DISPATCHER: Selecting module task -> '{next_task['task_id']}' (type: {next_task.get('task_type', 'unknown')}) ---")

            # V2.0 CONTRACT-FIRST: 调试输出
            if next_task['task_id'] == 'app_main':
                print(f"  -> 🎯 Found app_main task! Dependencies: {next_task.get('dependencies', [])}")
                dp_contract = state.get('device_dp_contract', [])
                print(f"  -> 📋 DP contract available: {len(dp_contract)} items")

            return {"current_module_task": next_task, "module_tasks": module_tasks[1:], "feedback": ""}

        print("--- L3 DISPATCHER: No more module tasks, finishing development ---")
        return {"current_module_task": None}


    def api_designer_node(state: AgentState) -> Dict:
        """
        通过“本地缓存 -> 本地模糊搜索 -> AI生成”三段式流程获取API。
        """
        task = state['current_module_task']
        if not task or task['task_type'] != 'driver' or task['task_id'] in ['config_manager', 'ota_handler', 'mqtt_logger',
                                                                            'tuya_handler']:
            return {"current_api_spec": None}

        peripheral = task['peripheral']
        print(f"--- L3: API DESIGNER: Searching for API for '{peripheral}' ---")

        # 阶段 1 & 2: 本地精确/模糊搜索
        cached_spec_str = find_api_spec(peripheral)
        if cached_spec_str:
            try:
                spec_json = json.loads(cached_spec_str)
                interface = None
                if isinstance(spec_json, dict):
                    for key, value in spec_json.items():
                        if isinstance(value, dict) and 'functions' in value:
                            interface = value
                            break

                if interface:
                    # 【修复】不仅传递functions，还要传递implementation_template和common_errors等重要信息
                    enhanced_spec = {
                        "functions": interface.get("functions", []),
                        "implementation_template": interface.get("implementation_template", {}),
                        "common_errors": interface.get("common_errors", []),
                        "critical_bug_prevention": interface.get("critical_bug_prevention", {}),
                        "validated_implementation": interface.get("validated_implementation", {})
                    }
                    formatted_spec = json.dumps(enhanced_spec, indent=2, ensure_ascii=False)
                    print(f"--- API DESIGNER: Successfully loaded enhanced API spec for '{peripheral}' from local cache. ---")
                    return {"current_api_spec": formatted_spec}
                else:
                    print(
                        f"--- API DESIGNER: Found local file for '{peripheral}', but content format is unexpected. Proceeding to AI generation. ---")
            except Exception as e:
                print(
                    f"--- API DESIGNER: Error parsing local API file for '{peripheral}': {e}. Proceeding to AI generation. ---")

        # 阶段 3: AI 生成 (作为备选)
        print(f"--- L3: API DESIGNER: No local API found. Generating with AI for '{peripheral}' ---")
        prompt = textwrap.dedent(f"""
        <Prompt>
            <Role>You are an expert API designer for embedded C/C++ drivers.</Role>
            <Goal>Generate a high-quality, detailed API specification in JSON format for the given peripheral or logical module.</Goal>
            <Context>
                <PeripheralOrModule>{peripheral}</PeripheralOrModule>
                <Task>{task['description']}</Task>
            </Context>
            <Instructions>
                1. Design a set of C-style functions.
                2. For communication modules like MQTT, design high-level functions like `connect`, `publish`, `subscribe`.
                3. The output must be a single, valid JSON object containing a root key `"{peripheral.upper().replace(' ', '_')}_Interface"` which contains a list of functions.
            </Instructions>
            <Example>
            For a 'DHT11', a good output would be:
            ```json
            {{
                "DHT11_Interface": {{
                    "functions": [
                        {{ "name": "dht11_setup", "description": "Initializes the DHT11 sensor on a specific pin.", "return_type": "void", "parameters": [{{"name": "pin", "type": "int"}}] }},
                        {{ "name": "dht11_read_temperature", "description": "Reads the temperature in Celsius.", "return_type": "float", "parameters": [] }},
                        {{ "name": "dht11_read_humidity", "description": "Reads the humidity in percent.", "return_type": "float", "parameters": [] }}
                    ]
                }}
            }}
            ```
            </Example>
            <OutputFormat>Return ONLY a fenced ```json code block with a single valid JSON object. No comments. No trailing commas. No extra text outside the code block.</OutputFormat>
        </Prompt>
        """)
        response = api_designer_model.invoke([HumanMessage(content=prompt)])
        resp_text = str(response.content).strip()
        # 优先尝试从代码块提取
        generated_spec_str = extract_code(resp_text, lang="json")
        if not generated_spec_str:
            # 如果失败，回退到正则搜索
            print("   -> Fallback: No JSON code block found. Searching for raw JSON object.")
            m = re.search(r'\{[\s\S]*\}', resp_text)
            generated_spec_str = m.group(0) if m else ""

        try:
            spec_json = json.loads(generated_spec_str)
            interface_key = f"{peripheral.upper().replace(' ', '_')}_Interface"
            interface = spec_json.get(interface_key)

            if not interface:
                # Fallback: 查找第一个包含 "functions" 键的子对象
                print(f"   -> Fallback: Root key '{interface_key}' not found. Searching for any valid interface...")
                for v in spec_json.values():
                    if isinstance(v, dict) and "functions" in v:
                        interface = v
                        break

            if not interface:
                raise ValueError("No interface with 'functions' found in generated spec.")

            functions = interface.get("functions", [])
            formatted_spec = json.dumps(functions, indent=2, ensure_ascii=False)
            print(f"--- API DESIGNER: Successfully generated API for '{peripheral}' using AI. ---")

            try:
                save_content = {interface_key: {"functions": functions}}
                save_filename = f"{peripheral.upper().replace(' ', '_')}_API_Package.json"
                save_filepath = API_PACKAGE_DIR / save_filename
                if not API_PACKAGE_DIR.exists():
                    API_PACKAGE_DIR.mkdir(parents=True, exist_ok=True)
                save_filepath.write_text(json.dumps(save_content, indent=4, ensure_ascii=False), encoding='utf-8')
                print(f"--- API DESIGNER: Saved newly generated API to local cache: '{save_filename}' ---")
            except Exception as e:
                print(f"--- API DESIGNER: WARNING - Failed to save newly generated API to cache: {e} ---")

            return {"current_api_spec": formatted_spec}
        except Exception as e:
            print(f"API DESIGNER GENERATION ERROR: {e}")
            return {"current_api_spec": f"// Failed to generate API for {peripheral}"}


    def derive_payload_contract(state) -> dict | None:
        """
        【V2.0 已修复】契约推断函数：只信任统一通信契约，不再回退。
        如果无法推断，则返回 None，强制上游处理此失败。
        """
        unified_contract = state.get('unified_communication_contract')
        current_device_task = state.get('current_device_task', {})
        device_role = current_device_task.get('device_role', '')

        # 调试日志，显示此函数被调用时的状态
        print(f"  -> Attempting to derive contract. Unified contract present: {unified_contract is not None}")

        if unified_contract and device_role:
            topic_map = unified_contract.get('topic_map', {})
            device_topics = topic_map.get(device_role, {"pub": [], "sub": []})
            topic = (device_topics.get("pub") or device_topics.get("sub") or [None])[0]

            if topic:
                dp_contract = state.get("device_dp_contract", [])
                key = (dp_contract or [{}])[0].get('code') or _derive_key_from_role(device_role)
                print(f"  -> ✅ Contract derived from UNIFIED plan: topic='{topic}', key='{key}'")
                return {"topic": topic, "json_key": key}

        # 【核心修改】如果无法从统一契约推断，则返回失败，不再进行启发式猜测
        print("  -> ❌ CRITICAL: Could not derive payload contract from the unified plan. Fallback is disabled.")
        return None

    def _assert_topic_compliance(code: str, contract: dict):
        """
        静态扫描生成的代码，确保包含预期的 MQTT 主题
        """
        if not contract or not contract.get('topic_map'):
            return  # 没有契约时跳过检查

        expected_topics = set()
        for dev in contract['topic_map'].values():
            expected_topics.update(dev.get('pub', []))
            expected_topics.update(dev.get('sub', []))

        if expected_topics and not any(t in code for t in expected_topics):
            raise ValueError(
                f"Generated code missing expected MQTT topic.\nExpect one of {expected_topics}"
            )


    def _derive_key_from_role(device_role: str) -> str:
        """根据设备角色推导JSON键名"""
        role_lower = device_role.lower()
        if any(k in role_lower for k in ["light", "lux", "illumination", "光照"]):
            return "illuminance"
        elif any(k in role_lower for k in ["temp", "temperature", "温度"]):
            return "temperature"
        elif any(k in role_lower for k in ["humid", "湿度"]):
            return "humidity"
        elif any(k in role_lower for k in ["alarm", "报警", "警报"]):
            return "alarm_status"
        else:
            return "value"

    def _violates_json_contract(code: str, contract: dict) -> str:
        """
        检查生成的代码是否违反了载荷契约。
        返回违反原因的字符串，如果符合契约则返回空字符串。
        """
        if not code:
            return "empty code"

        reasons = []
        has_publish = "localMqttClient.publish(" in code
        uses_json = ("#include <ArduinoJson.h>" in code) and ("serializeJson(" in code)

        if has_publish and not uses_json:
            reasons.append("must include <ArduinoJson.h> and use serializeJson()")

        if has_publish and contract:
            if contract["topic"] not in code:
                reasons.append(f"must publish to literal topic '{contract['topic']}'")
            key = contract["json_key"]
            if f'doc["{key}"]' not in code and f'"{key}":' not in code:
                reasons.append(f"JSON must contain key '{key}'")

        return "; ".join(reasons)

    def _contains_forbidden_apis(code: str, forbidden_apis: list) -> str:
        """
        检查生成的代码是否包含禁止的API调用（第四层防御）。
        返回违反原因的字符串，如果没有违反则返回空字符串。
        """
        if not code or not forbidden_apis:
            return ""

        violations = []
        code_lines = code.split('\n')

        for api in forbidden_apis:
            # 检查函数调用形式，如 analogRead(, bh1750_read_lux(
            api_call_pattern = f"{api}("

            for line_num, line in enumerate(code_lines, 1):
                # 跳过注释行
                stripped_line = line.strip()
                if stripped_line.startswith('//') or stripped_line.startswith('/*'):
                    continue

                if api_call_pattern in line:
                    violations.append(f"Line {line_num}: Forbidden API '{api}' detected in: {line.strip()}")

        if violations:
            return f"FORBIDDEN API VIOLATION: {'; '.join(violations)}"

        return ""

    def _validate_task_contract(code: str, task_contract: dict) -> str:
        """
        基于任务契约验证代码合规性（第四层防御的核心函数）。
        结合正向契约检查和反向契约检查。
        """
        if not code or not task_contract:
            return ""

        violations = []

        # 1. 检查禁止的API（反向契约）
        forbidden_apis = task_contract.get('forbidden_hardware_apis', [])
        if forbidden_apis:
            forbidden_violation = _contains_forbidden_apis(code, forbidden_apis)
            if forbidden_violation:
                violations.append(forbidden_violation)

        # 2. 检查数据源约束
        data_source = task_contract.get('data_source', 'local')
        if data_source == 'remote':
            # 远程数据源设备不应该有任何传感器读取逻辑
            sensor_patterns = ['analogRead(', 'bh1750_', 'ds1624_', 'apds9960_', 'dht22_']
            for pattern in sensor_patterns:
                if pattern in code:
                    violations.append(f"Remote data source device must not contain sensor reading pattern: {pattern}")

        # 3. 检查MQTT订阅逻辑（对于远程数据源设备）
        if data_source == 'remote':
            if 'localMqttClient.subscribe(' not in code and 'tuyaMqttClient.subscribe(' not in code:
                violations.append("Remote data source device must implement MQTT subscription logic")

        # 4. CRITICAL: 检查传感器初始化与数据读取的完整性
        sensor_init_read_pairs = [
            ('bh1750_init(', 'bh1750_read_lux()', 'BH1750 light sensor'),
            ('hcsr04_init(', 'hcsr04_read_distance_cm()', 'HC-SR04 ultrasonic sensor'),
            ('dht22_init(', 'dht22_read_temperature()', 'DHT22 temperature sensor'),
            ('ds1624_init(', 'ds1624_read_temperature()', 'DS1624 temperature sensor'),
            ('apds9960_init(', 'apds9960_read_', 'APDS9960 gesture sensor')
        ]

        for init_func, read_func, sensor_name in sensor_init_read_pairs:
            if init_func in code and read_func not in code:
                violations.append(f"CRITICAL: {sensor_name} is initialized with {init_func} but data is never read with {read_func} in loop()")

        # 5. CRITICAL: 检查NAN使用是否有正确的头文件包含
        if 'NAN' in code or 'isnan(' in code:
            if '#include <math.h>' not in code and '#include <Arduino.h>' not in code:
                violations.append("CRITICAL: Code uses NAN or isnan() but missing required headers (#include <math.h> and/or #include <Arduino.h>)")

        return "; ".join(violations) if violations else ""

    def _validate_communication_contract(code: str, unified_contract: dict, device_role: str) -> str:
        """
        验证代码是否遵循统一通信契约（第四层防御）。
        检查是否使用了正确的 MQTT 主题。
        """
        if not code or not unified_contract or not device_role:
            return ""

        violations = []
        topic_map = unified_contract.get('topic_map', {})
        device_topics = topic_map.get(device_role, {"pub": [], "sub": []})

        # 检查是否使用了错误的占位符主题
        # 注意：/sensor/light 是合法的topic，不应该被禁用
        forbidden_topics = ['///data', '/sensor/value']
        for forbidden_topic in forbidden_topics:
            if f'"{forbidden_topic}"' in code:
                violations.append(f"Using forbidden placeholder topic: {forbidden_topic}")

        # 检查是否使用了正确的主题
        expected_topics = device_topics["pub"] + device_topics["sub"]
        if expected_topics:
            found_correct_topic = False
            for topic in expected_topics:
                if f'"{topic}"' in code:
                    found_correct_topic = True
                    break

            if not found_correct_topic:
                violations.append(f"Missing required communication topics: {expected_topics}")

        return "; ".join(violations) if violations else ""

    def developer_node(state: AgentState) -> Dict:
        feedback = state.get('feedback', '')
        task = state.get('current_module_task')

        def clean_module_filename(filename: str) -> str:
            """清理文件名，移除多重后缀（如 .ino.cpp）并返回模块ID"""
            for ext in ('.ino.cpp', '.cpp', '.c', '.cc', '.cxx', '.ino'):
                if filename.endswith(ext):
                    return filename[:-len(ext)]
            return Path(filename).stem

        if not task and "FAIL" in feedback:
            # 导入智能日志函数
            from app.services.workflow_service import _log_with_personality, analyze_compilation_error

            workflow_id = state.get('workflow_id')
            _log_with_personality(workflow_id, "编译失败了！让我看看哪里出了问题...", "error", delay=0.5)

            # 使用智能错误分析
            error_analysis = analyze_compilation_error(feedback)

            # 显示错误定位信息
            if error_analysis["line_number"]:
                _log_with_personality(workflow_id,
                    f"我发现错误出现在 {error_analysis['file_location']} 第{error_analysis['line_number']}行：{error_analysis['error_message']}",
                    "analyzing", delay=1.0)

            # 显示AI的思考过程
            _log_with_personality(workflow_id, error_analysis["ai_thought"], "thinking", delay=1.0)

            faulty_module_id = None

            # 主要推断模式：从错误行直接解析文件路径
            # 允许行首直接出现 src|lib；允许 Windows 驱动器前缀；支持多种源文件后缀与可选列号
            primary_pattern = re.compile(
                r'^(?P<filepath>(?:[A-Za-z]:)?(?:.*?[/\\])?(?:src|lib)[/\\][^/\\:\n]+\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?)'
                r':\d+(?::\d+)?:\s+(?:fatal\s+)?error:',
                re.MULTILINE | re.IGNORECASE
            )
            match = primary_pattern.search(feedback)

            if match:
                filepath = Path(match.group('filepath'))
                faulty_module_id = clean_module_filename(filepath.name)
                print(f"--- [DEVELOPER] Inferred faulty module from error line: '{filepath}' -> '{faulty_module_id}' ---")
            else:
                # 备用推断模式：从各种构建系统的失败汇总行解析（.pio、.bld、make、ninja 都可匹配）
                fallback_pattern = re.compile(
                    r"\*\*\*\s*\[.*?[/\\](?:src|lib)[/\\].*?([^\s/\\]+?)\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?\.o\]\s+Error\b",
                    re.MULTILINE | re.IGNORECASE
                )
                match = fallback_pattern.search(feedback)
                if match:
                    faulty_module_id_with_ext = match.group(1)
                    faulty_module_id = clean_module_filename(faulty_module_id_with_ext)
                    print(f"--- [DEVELOPER] Inferred faulty module using generic summary pattern: '{faulty_module_id_with_ext}' -> '{faulty_module_id}' ---")

            # 兜底模式：匹配"Compiling .../src|lib/...xxx.cpp.o"的行
            if not faulty_module_id:
                compiling_pattern = re.compile(
                    r"Compiling\s+.*?[/\\](?:src|lib)[/\\].*?([^\s/\\]+?)\.(?:c|cc|cpp|cxx|ino)(?:\.cpp)?\.o",
                    re.IGNORECASE
                )
                match = compiling_pattern.search(feedback)
                if match:
                    faulty_module_id = clean_module_filename(match.group(1))
                    print(f"--- [DEVELOPER] Inferred from 'Compiling' line: '{faulty_module_id}' ---")

            if not faulty_module_id:
                faulty_module_id = "app_main"
                print(f"--- [DEVELOPER] Could not infer module from logs. Defaulting to '{faulty_module_id}'. ---")

            original_plan = state.get('original_module_plan', [])
            faulty_task_details = next((m for m in original_plan if m['task_id'] == faulty_module_id), None)

            if faulty_task_details:
                _log_with_personality(workflow_id, f"现在我来修复 {faulty_module_id} 模块...让我重新分析和编写代码", "fixing", delay=1.0)
                task = faulty_task_details
            else:
                _log_with_personality(workflow_id, f"无法找到 {faulty_module_id} 模块的详细信息，修复失败", "error")
                return {"feedback": f"FAIL: Could not identify faulty module '{faulty_module_id}' for repair."}

        if not task:
            return {}

        device_id = state['current_device_task']['internal_device_id']
        task_id = task['task_id']
        workflow_id = state.get('workflow_id')
        device_role = state['current_device_task'].get('device_role', '设备')

        # 导入智能日志函数（如果还没导入）
        from app.services.workflow_service import _log_with_personality

        # 根据任务类型生成智能开发日志
        if task.get('task_type') == 'driver' and task.get('peripheral'):
            peripheral_name = task.get('peripheral', '组件')
            _log_with_personality(workflow_id, f"现在我来开发 {task_id}.h 文件...这是{peripheral_name}的驱动头文件，需要定义核心接口", "coding")
        elif task_id == 'app_main':
            _log_with_personality(workflow_id, f"现在开发主程序 app_main.ino...整合所有模块，实现{device_role}的完整逻辑", "coding")
        elif task_id in ['config_manager', 'ota_handler', 'mqtt_logger', 'tuya_handler']:
            module_descriptions = {
                'config_manager': '配置管理模块，处理设备参数和网络配置',
                'ota_handler': 'OTA升级模块，支持无线固件更新',
                'mqtt_logger': 'MQTT日志模块，实现远程日志传输',
                'tuya_handler': '涂鸦云模块，处理云端通信协议'
            }
            desc = module_descriptions.get(task_id, f'{task_id}模块')
            _log_with_personality(workflow_id, f"现在开发 {task_id} 模块...{desc}", "coding")
        else:
            _log_with_personality(workflow_id, f"开始开发 {task_id} 模块...", "coding")

        print(f"--- L3: DEVELOPER: Coding module '{task_id}' for device '{device_id}' ---")

        feedback_context = ""
        if "FAIL" in feedback:
            # 核心修正：截断过长的错误日志，只保留最后8000个字符
            MAX_FEEDBACK_LENGTH = 8000
            trimmed_feedback = feedback
            if len(trimmed_feedback) > MAX_FEEDBACK_LENGTH:
                print(
                    f"  [INFO] Feedback log is too long ({len(trimmed_feedback)} chars). Truncating to last {MAX_FEEDBACK_LENGTH} chars.")
                trimmed_feedback = feedback[-MAX_FEEDBACK_LENGTH:]

            feedback_context = textwrap.dedent(f"""
            <Feedback_From_Previous_Attempt>
            IMPORTANT: Analyze and fix the following compilation error(s). The error log is provided below inside a text block.

            ```text
            {trimmed_feedback}
            ```

            Hints:
            - 'conflicting declaration' is often caused by duplicate globals/singletons. Define globals in .cpp and use extern in .h.
            - 'was not declared in this scope' usually means a missing #include directive.
            - Review the pin types (int vs gpio_num_t) and ensure they match the driver function signatures.
            </Feedback_From_Previous_Attempt>
            """)

        completed_modules = state.get('completed_modules', {})
        version = completed_modules.get(task_id, {}).get('version', 0) + 1

        # CRITICAL: Auto-generate tuya_handler if cloud credentials exist but module is missing
        if (task_id == 'tuya_handler' or
            (task_id not in ['config_manager', 'ota_handler', 'mqtt_logger'] and
            'tuya_handler' not in completed_modules and
            state.get('cloud_product_id') is not None)):
            if task_id != 'tuya_handler':
                print(f"  -> CRITICAL: Cloud credentials detected but tuya_handler missing. Auto-generating tuya_handler first.")
                # Generate tuya_handler module
                header_code = generate_tuya_handler_header()
                source_code = generate_tuya_handler_source()
                sha256_h_content = generate_mbedtls_sha256_header()
                sha256_cpp_content = generate_mbedtls_sha256_source()
                source_files_dict = {"tuya_handler.cpp": source_code, "SHA256.h": sha256_h_content,
                                    "SHA256.cpp": sha256_cpp_content}
                completed_modules['tuya_handler'] = {"task_id": 'tuya_handler', "header_code": header_code,
                                                    "source_code": json.dumps(source_files_dict), "main_code": None,
                                                    "version": 1}
                print("  -> Auto-generated tuya_handler module for cloud connectivity")

        if task_id == 'config_manager':
            header_code = generate_config_manager_header(
                device_id=state['current_device_task']['internal_device_id'],
                wifi_ssid=state.get('wifi_ssid'),
                wifi_password=state.get('wifi_password'),
                product_id=state.get('cloud_product_id'),
                cloud_device_id=state.get('cloud_device_id'),
                device_secret=state.get('cloud_device_secret')
            )
            completed_modules[task_id] = {"task_id": task_id, "header_code": header_code, "source_code": None,
                                        "main_code": None, "version": version}
            return {"completed_modules": completed_modules, "feedback": "", "current_module_task": task}

        if task_id == 'ota_handler':
            header_code = generate_ota_handler_header()
            source_code = generate_ota_handler_source()
            completed_modules[task_id] = {"task_id": task_id, "header_code": header_code, "source_code": source_code,
                                        "main_code": None, "version": version}
            return {"completed_modules": completed_modules, "feedback": "", "current_module_task": task}

        if task_id == 'mqtt_logger':
            header_code = generate_mqtt_logger_header()
            source_code = generate_mqtt_logger_source()
            completed_modules[task_id] = {"task_id": task_id, "header_code": header_code, "source_code": source_code,
                                        "main_code": None, "version": version}
            return {"completed_modules": completed_modules, "feedback": "", "current_module_task": task}

        if task_id == 'tuya_handler':
            header_code = generate_tuya_handler_header()
            source_code = generate_tuya_handler_source()
            sha256_h_content = generate_mbedtls_sha256_header()
            sha256_cpp_content = generate_mbedtls_sha256_source()
            source_files_dict = {"tuya_handler.cpp": source_code, "SHA256.h": sha256_h_content,
                                "SHA256.cpp": sha256_cpp_content}
            completed_modules[task_id] = {"task_id": task_id, "header_code": header_code,
                                        "source_code": json.dumps(source_files_dict), "main_code": None,
                                        "version": version}
            return {"completed_modules": completed_modules, "feedback": "", "current_module_task": task}

        api_spec = state.get('current_api_spec', 'No API specification provided.')
        context, instructions = "", ""
        if task['task_type'] == 'driver':
            context = f"<APISpecification>\\n{api_spec}\\n</APISpecification>"
            task_id = task['task_id']
            instructions = f"""
                                <DevelopmentEnvironment>
                                **Target Platform**: Arduino-ESP32 Core with ESP-IDF backend (v4.4–v5.0 compatibility)
                                **Architecture**: Arduino application layer + ESP-IDF driver layer
                                **Driver Requirements**: Use ESP-IDF native functions (driver/i2c.h, driver/gpio.h) for all hardware drivers
                                **Application Layer**: Arduino-style APIs (WiFi.h, PubSubClient.h) are acceptable in app_main.ino only
                                **Compatibility Rule**: Prefer ESP-IDF fields and functions that are common across IDF v4.4–v5.0 unless version-guarded

                                **CRITICAL ERROR PREVENTION RULES:**
                                1. **GPIO Type Casting**: ALWAYS cast integer pin numbers to gpio_num_t when calling ESP-IDF GPIO functions
                                - WRONG: gpio_set_level(pin, 1)
                                - CORRECT: gpio_set_level((gpio_num_t)pin, 1)
                                - WRONG: gpio_get_level(echo_pin)
                                - CORRECT: gpio_get_level((gpio_num_t)echo_pin)
                                2. **Delay Functions**: NEVER use esp_rom_delay_us() - it's not available in all ESP-IDF versions
                                - WRONG: esp_rom_delay_us(10)
                                - CORRECT: ets_delay_us(10) or delayMicroseconds(10)
                                3. **Include Required Headers**: Always include necessary headers for delay functions
                                - For ets_delay_us(): #include <esp_timer.h> or #include <rom/ets_sys.h>
                                4. **I2C Port Type**: When using I2C functions, cast port numbers to i2c_port_t
                                - WRONG: i2c_driver_install(port, ...)
                                - CORRECT: i2c_driver_install((i2c_port_t)port, ...)
                                5. **Function Parameter Types**: Always check ESP-IDF function signatures and cast parameters accordingly
                                - All GPIO functions require gpio_num_t casting
                                - All I2C functions require i2c_port_t casting for port parameter
                                6. **BH1750 Sensor Critical Requirements**: If developing BH1750 driver, you MUST follow this sequence:
                                - WRONG: bh1750_init() -> bh1750_read_lux() (will fail - no measurement mode set)
                                - CORRECT: bh1750_init() -> bh1750_set_mode(BH1750_MODE_CONTINUOUS_HIGH_RES) -> delay(120ms) -> bh1750_read_lux()
                                - The sensor REQUIRES measurement mode to be set after power-on before any readings
                                - Use continuous mode for repeated readings, one-time mode requires re-setting before each read
                                7. **CRITICAL: NAN Declaration Requirements**: When using NAN in driver code, you MUST include proper headers:
                                - ALWAYS include: #include <math.h>
                                - For ESP32/Arduino: Also include #include <Arduino.h> (provides NAN definition)
                                - WRONG: return NAN; (without proper includes - will cause compilation error)
                                - CORRECT: #include <math.h> + #include <Arduino.h> then return NAN;
                                - Alternative: Use nanf("") or std::numeric_limits<float>::quiet_NaN() with <limits>
                                </DevelopmentEnvironment>

                                <Instructions>
                                1.  **Goal**: Your task is to implement the C++ header (.h) and source (.cpp) files for the driver based *only* on the provided `<APISpecification>`.
                                2.  **API Implementation**: You MUST implement all functions declared in the `<APISpecification>` without changing their public signatures in the header file.
                                3.  **Helper Functions**: You MAY add private `static` helper functions within the source (`.cpp`) file to keep the implementation clean, readable, and robust. These helper functions MUST NOT be declared in the header (`.h`) file.
                                4.  **Completeness**: Provide the complete code for both the header and the source file. Do not omit any part.
                                5.  **Header File Guard**: The header file MUST include standard header guards (`#ifndef`, `#define`, `#endif`).
                                6.  **ESP32 I2C Driver**: If implementing an I2C peripheral for ESP32, you MUST use the ESP-IDF driver functions from `<driver/i2c.h>`. Do NOT use Arduino Wire.h library.
                                7.  **I2C Read Termination**: For the final byte read, use the correct termination based on function type:
                                    - If using `i2c_master_read(cmd, buf, len, ack)`: use `I2C_MASTER_LAST_NACK` for the final byte
                                    - If using `i2c_master_read_byte(cmd, data, ack_en)`: use `I2C_MASTER_NACK` as the third argument
                                    - Do NOT use `0` or `I2C_MASTER_ACK` for the final read operation
                                8.  **No `main()`**: Do not include a `main()` function or `setup()`/`loop()` unless the API spec explicitly requires it. These are library files.
                                9.  **No Placeholders**: Your code must be fully implemented and functional. Do not leave placeholder comments like `// Your implementation here`.
                                10. **Critical C++ Syntax Rule**: When assigning values to struct members after declaration, you MUST use the standard assignment syntax (e.g., `variable.member = value;`). You MUST NOT use the C-style designated initializer syntax with a leading dot (e.g., `.member = value;`) in an assignment context.
                                11. **CRITICAL: File Naming Rule**: All file names and #include statements MUST use ONLY English characters, numbers, and underscores. NO Chinese characters are allowed. The header file name should be `{sanitize_filename(task_id)}.h` and the source file should be `{sanitize_filename(task_id)}.cpp`.

                                <Rule_ID>CRITICAL_DEPENDENCY_RULE</Rule_ID>
                                <Rule_Description>
                                11. **MANDATORY Header Inclusion**: You MUST ensure that for every standard library function, macro, or type you use, you include the corresponding standard header file. This is a non-negotiable requirement for generating correct, compilable code. Examples:
                                    - For mathematical functions like `sqrt`, `pow`, or the `NAN` macro, you MUST `#include <math.h>`.
                                    - For string manipulation functions like `strcpy`, `memset`, or `strlen`, you MUST `#include <string.h>`.
                                    - For standard types like `uint8_t`, `uint16_t`, `int32_t`, you MUST `#include <stdint.h>`.
                                    - For ESP32 logging functions like `ESP_LOGE`, `ESP_LOGI`, you MUST `#include <esp_log.h>`.
                                    - For GPIO functions and types like `gpio_num_t`, `GPIO_PULLUP_ENABLE`, you MUST `#include <driver/gpio.h>`.
                                    - For I2C functions and types like `i2c_port_t`, `i2c_config_t`, you MUST `#include <driver/i2c.h>`.
                                12. **Header Verification**: Before finalizing your code, mentally verify that every function, macro, and type you use has its corresponding `#include` statement at the top of the file.
                                19. **MANDATORY: MQTT Callback as a Dispatcher**: The `local_mqtt_callback` function MUST act as a central message dispatcher. It must FIRST pass all incoming messages to `ota_handle_mqtt_message` for potential OTA command processing. THEN, it MUST contain separate `if` or `else if` statements to check the topic for application-specific data (e.g., `/smart_system/light_collector/data`) and execute the corresponding business logic. It is CRITICAL that application data is processed AFTER the OTA handler check.
                                </Rule_Description>

                                <CriticalBugPrevention>
                                CRITICAL: TIMER INITIALIZATION BUG PREVENTION

                                **MANDATORY RULE FOR TIMEOUT LOGIC:**
                                When implementing timeout checks in loops, you MUST initialize the timer variable BEFORE entering the loop, NOT after the condition is met.

                                **WRONG PATTERN (CAUSES BUGS):**
                                ```cpp
                                int64_t start = 0;  // WRONG: Initialized to 0
                                while (condition) {{
                                    if (esp_timer_get_time() - start > timeout) break;  // WRONG: Uses uninitialized start
                                }}
                                start = esp_timer_get_time();  // WRONG: Too late!
                                ```

                                **CORRECT PATTERN (REQUIRED):**
                                ```cpp
                                int64_t start_wait = esp_timer_get_time();  // CORRECT: Initialize BEFORE loop
                                while (condition) {{
                                    if (esp_timer_get_time() - start_wait > timeout) break;  // CORRECT: Uses initialized timer
                                }}
                                int64_t pulse_start = esp_timer_get_time();  // CORRECT: Separate timer for measurement
                                ```

                                **SPECIFIC FOR ULTRASONIC SENSORS:**
                                Use separate timers: start_wait for timeout during echo HIGH wait, then pulse_start/pulse_end for actual pulse duration measurement.

                                This rule applies to ALL timeout implementations, especially in sensor drivers.
                                </CriticalBugPrevention>
                                </Instructions>

                                <Correct_Syntax_Example>
                                // First, declare and zero-initialize the struct
                                i2c_config_t my_conf = {{}};
                                // Then, assign values to its members
                                my_conf.mode = I2C_MODE_MASTER;
                                my_conf.master.clk_speed = 100000;
                                </Correct_Syntax_Example>

                                <Incorrect_Syntax_to_Avoid>
                                i2c_config_t my_conf;
                                .mode = I2C_MODE_MASTER; // <-- WRONG SYNTAX, DO NOT GENERATE!
                                </Incorrect_Syntax_to_Avoid>
                                <CorrectExample for ESP32 I2C Driver>
                            [HEADER]
                            ```cpp
                            #ifndef SIMPLE_I2C_DRIVER_H
                            #define SIMPLE_I2C_DRIVER_H
                            #include <stdint.h>
                            #include "esp_err.h"
                            #include "driver/i2c.h"
                            #include "driver/gpio.h"

                            void simple_i2c_init(i2c_port_t port, int sda_pin, int scl_pin);
                            esp_err_t simple_i2c_read_byte(i2c_port_t port, uint8_t device_addr, uint8_t reg_addr, uint8_t *data);
                            #endif
                            ```

                            [SOURCE]
                            ```cpp
                            #include "simple_i2c_driver.h"
                            #include "esp_log.h"

                            void simple_i2c_init(i2c_port_t port, int sda_pin, int scl_pin) {{
                                i2c_config_t conf = {{}}; // Zero-initialize
                                conf.mode = I2C_MODE_MASTER;
                                conf.sda_io_num = sda_pin;
                                conf.scl_io_num = scl_pin;
                                conf.sda_pullup_en = GPIO_PULLUP_ENABLE;
                                conf.scl_pullup_en = GPIO_PULLUP_ENABLE;
                                conf.master.clk_speed = 100000;
                                i2c_param_config(port, &conf);
                                i2c_driver_install(port, conf.mode, 0, 0, 0);
                            }}

                            esp_err_t simple_i2c_read_byte(i2c_port_t port, uint8_t device_addr, uint8_t reg_addr, uint8_t *data) {{
                                // A minimal implementation example
                                i2c_cmd_handle_t cmd = i2c_cmd_link_create();
                                i2c_master_start(cmd);
                                i2c_master_write_byte(cmd, (device_addr << 1) | I2C_MASTER_WRITE, true);
                                i2c_master_write_byte(cmd, reg_addr, true);
                                i2c_master_start(cmd);
                                i2c_master_write_byte(cmd, (device_addr << 1) | I2C_MASTER_READ, true);
                                i2c_master_read_byte(cmd, data, I2C_MASTER_NACK); // Correctly use NACK for the last byte
                                i2c_master_stop(cmd);
                                esp_err_t ret = i2c_master_cmd_begin(port, cmd, 1000 / portTICK_PERIOD_MS);
                                i2c_cmd_link_delete(cmd);
                                return ret;
                            }}
                            ```

                            <BH1750_Critical_Example>
                            **CRITICAL: BH1750 Light Sensor Correct Usage Pattern**
                            ```cpp
                            // WRONG - Will cause sensor to return stale/invalid data
                            void setup() {{
                                bh1750_init(I2C_NUM_0, GPIO_NUM_17, GPIO_NUM_4, false);
                                // Missing bh1750_set_mode() - CRITICAL ERROR!
                            }}
                            void loop() {{
                                float lux = bh1750_read_lux(); // Will return invalid data
                            }}

                            // CORRECT - Proper initialization and usage
                            void setup() {{
                                bh1750_init(I2C_NUM_0, GPIO_NUM_17, GPIO_NUM_4, false);
                                bh1750_set_mode(BH1750_MODE_CONTINUOUS_HIGH_RES); // MANDATORY!
                                delay(120); // Wait for first measurement (120ms for high-res mode)
                            }}
                            void loop() {{
                                float lux = bh1750_read_lux(); // Now works correctly
                                // No need to call bh1750_set_mode() again in continuous mode
                            }}
                            ```
                            </BH1750_Critical_Example>

                            <NAN_Usage_Critical_Example>
                            **CRITICAL: Correct NAN Usage Pattern**
                            ```cpp
                            // WRONG - Missing headers, will cause compilation error
                            float sensor_read() {{
                                if (error_condition) {{
                                    return NAN; // ERROR: 'NAN' was not declared in this scope
                                }}
                            }}

                            // CORRECT - Proper headers included
                            #include <Arduino.h>  // Provides NAN definition
                            #include <math.h>     // Provides isnan() function

                            float sensor_read() {{
                                if (error_condition) {{
                                    return NAN; // Now works correctly
                                }}
                                return valid_value;
                            }}

                            void loop() {{
                                float value = sensor_read();
                                if (isnan(value)) {{  // Proper error checking
                                    value = -1.0f;    // Convert to sentinel value
                                }}
                            }}
                            ```
                            </NAN_Usage_Critical_Example>
                            </CorrectExample>
                                <OutputFormat>
                            You MUST provide two distinct code blocks, one for the header and one for the source file. Use the specified markdown format.
                                [HEADER]
                                ```cpp
                                // Header file content for {sanitize_filename(task_id)}.h
                                ```
                            [SOURCE]
                                ```cpp
                                // Source file content for {sanitize_filename(task_id)}.cpp
                                ```
                                </OutputFormat>
                                """
            prompt = textwrap.dedent(f"""
            <Prompt>
                <Role>You are an expert embedded systems developer following a strict modular architecture.</Role>
                <DevelopmentEnvironment>
                    **Target Platform**: Arduino-ESP32 Core with ESP-IDF backend (v4.4–v5.0 compatibility)
                    **Architecture**: Arduino application layer + ESP-IDF driver layer
                    **Driver Requirements**: Use ESP-IDF native functions (driver/i2c.h, driver/gpio.h) for all hardware drivers
                    **Application Layer**: Arduino-style APIs (WiFi.h, PubSubClient.h) are acceptable in app_main.ino only
                    **Compatibility Rule**: Prefer ESP-IDF fields and functions that are common across IDF v4.4–v5.0 unless version-guarded
                </DevelopmentEnvironment>
                <Context>
                    <TaskDescription>{task['description']}</TaskDescription>
                    {feedback_context}
                    {context}
                </Context>
                {instructions}
            </Prompt>
            """)
            response = developer_model.invoke([HumanMessage(content=prompt)])
            content = response.content
            header_code = extract_code(content, lang="cpp", block_name="HEADER")
            source_code = extract_code(content, lang="cpp", block_name="SOURCE")
            completed_modules[task_id] = {"task_id": task_id, "header_code": header_code, "source_code": source_code,
                                        "main_code": None, "version": version}

            # V2.0 CONTRACT-FIRST: 修复关键bug - driver任务完成后必须返回
            print(f"  -> ✅ Driver module '{task_id}' completed successfully")
            return {"completed_modules": completed_modules, "feedback": "", "current_module_task": task}

        else:  # application
            print("--- L3: DEVELOPER: Coding main application 'app_main.ino' ---")
            print(f"  -> 🎯 Processing app_main task: {task_id}")

            # V2.0 CONTRACT-FIRST: 调试契约注入
            dp_contract = state.get("device_dp_contract", [])
            print(f"  -> 📋 DP contract items: {len(dp_contract)}")
            if dp_contract:
                print(f"  -> 📝 Sample DP: {dp_contract[0].get('name', 'Unknown')} ({dp_contract[0].get('code', 'unknown')})")

            # 【核心修改】在所有操作之前，强制检查 contract 是否存在
            payload_contract = state.get("payload_contract")
            if not payload_contract:
                # 尝试最后一次从权威来源派生
                payload_contract = derive_payload_contract(state)

            # 如果此时 contract 仍然不存在，说明流程上游存在致命错误，必须停止
            if not payload_contract:
                error_msg = "FAIL: CRITICAL - `payload_contract` is missing. The `unified_communication_contract` was likely lost during state transition. Aborting development."
                print(f"  -> {error_msg}")
                return {"feedback": error_msg}

            print(f"  -> ✅ Using authoritative payload contract: topic='{payload_contract['topic']}', key='{payload_contract['json_key']}'")

            # V2.0 CONTRACT-FIRST: 使用DP契约中的实际code覆盖推导的key
            if dp_contract and payload_contract:
                # 使用第一个DP的code作为JSON key（通常是主要的传感器数据）
                actual_key = dp_contract[0].get('code', payload_contract['json_key'])
                if actual_key != payload_contract['json_key']:
                    print(f"  -> Overriding payload key: '{payload_contract['json_key']}' -> '{actual_key}' (from DP contract)")
                    payload_contract['json_key'] = actual_key

                # 🔧 DEBUG: 确认最终传递给LLM的契约内容
                print(f"  -> 🎯 FINAL payload contract for LLM: topic='{payload_contract['topic']}', key='{payload_contract['json_key']}'")
                print(f"  -> 🎯 DP contract source: {dp_contract[0]}")

            # 🔧 CRITICAL: 如果有反馈说明JSON契约违规，添加强化提示
            if feedback and "JSON Contract" in feedback and "must contain key" in feedback:
                print(f"  -> 🚨 PREVIOUS ATTEMPT FAILED: {feedback}")
                print(f"  -> 🎯 MUST USE EXACT KEY: '{payload_contract['json_key']}'")
                feedback_context += f"""

                <CRITICAL_PREVIOUS_FAILURE>
                🚨🚨🚨 PREVIOUS CODE GENERATION FAILED DUE TO JSON CONTRACT VIOLATION 🚨🚨🚨
                {feedback}

                🔧 MANDATORY FIX REQUIRED - NO EXCEPTIONS:
                - You MUST use the exact JSON key: "{payload_contract['json_key']}"
                - DO NOT use "lux", "value", "illuminance_lux", or any other key
                - The JSON payload MUST be: {{"\\"{payload_contract['json_key']}\\"": <sensor_value>}}
                - Your code MUST contain: doc["{payload_contract['json_key']}"] = sensor_value;
                - This is CRITICAL - contract validation will reject any other key
                - REMEMBER: The exact required key is "{payload_contract['json_key']}"
                </CRITICAL_PREVIOUS_FAILURE>
                """

            # 🎯 添加契约强制提醒到提示词开头
            contract_reminder = f"""
            🚨🚨🚨 CRITICAL CONTRACT REQUIREMENTS - READ FIRST 🚨🚨🚨

            Before you write ANY code, remember these MANDATORY requirements:

            1. JSON KEY: You MUST use exactly "{payload_contract['json_key']}" as the JSON key
            2. MQTT TOPIC: You MUST publish to exactly "{payload_contract['topic']}"
            3. Your code MUST contain: doc["{payload_contract['json_key']}"] = sensor_value;
            4. NO variations or alternatives are allowed

            The automated validation system will scan your code for these exact patterns.
            Any deviation will cause immediate rejection and regeneration.

            🎯 REMEMBER: JSON key = "{payload_contract['json_key']}", Topic = "{payload_contract['topic']}"

            """

            # V4 新增：获取统一通信契约信息
            unified_contract = state.get('unified_communication_contract', {})
            device_task = state['current_device_task']
            device_role = device_task.get('device_role', '') if device_task else ''
            communication_context = ""

            if unified_contract and device_role:
                topic_map = unified_contract.get('topic_map', {})
                device_topics = topic_map.get(device_role, {"pub": [], "sub": []})
                schema_map = unified_contract.get('schema', {})

                comm_instructions = []
                payload_schema_instructions = []

                if device_topics["pub"]:
                    pub_topics = ', '.join(device_topics["pub"])
                    comm_instructions.append(f"PUBLISH data to: {pub_topics}")

                if device_topics["sub"]:
                    sub_topics = ', '.join(device_topics["sub"])
                    comm_instructions.append(f"SUBSCRIBE to: {sub_topics}")

                    # 【核心修复】为订阅的主题添加 payload schema 信息
                    for sub_topic in device_topics["sub"]:
                        if sub_topic in schema_map:
                            schema_info = schema_map[sub_topic]
                            publisher = schema_info.get('publisher', 'Unknown')
                            payload_schema = schema_info.get('payload_schema', {})

                            if payload_schema:
                                schema_keys = list(payload_schema.keys())
                                payload_schema_instructions.append(
                                    f"Topic '{sub_topic}' (from {publisher}) contains JSON keys: {', '.join(schema_keys)}"
                                )
                                print(f"  -> 🔧 Found payload schema for subscribed topic '{sub_topic}': {schema_keys}")

                if comm_instructions:
                    # 获取 JSON key 信息
                    dp_contract = state.get("device_dp_contract", [])
                    json_key = (dp_contract or [{}])[0].get('code') or _derive_key_from_role(device_role)

                    # 构建完整的通信契约，包含 JSON 格式要求和 payload schema
                    schema_context = ""
                    if payload_schema_instructions:
                        schema_context = f"""
                    **SUBSCRIBED TOPIC PAYLOAD SCHEMAS:**
                    {chr(10).join(payload_schema_instructions)}

                    **CRITICAL: When parsing incoming MQTT messages, you MUST use the exact JSON keys listed above.**
                    """

                    # 构建完整的通信契约，包含 JSON 格式要求
                    communication_context = textwrap.dedent(f"""
                    <CommunicationContract>
                    {json.dumps(unified_contract, indent=2)}
                    </CommunicationContract>

                    <CommunicationInstructions>
                    **MANDATORY MQTT COMMUNICATION (ABSOLUTE REQUIREMENT):**
                    {' and '.join(comm_instructions)}
                    {schema_context}
                    **CRITICAL IMPLEMENTATION RULES:**
                    1. You MUST use these EXACT topic strings in your MQTT calls - do not modify them
                    2. For SUBSCRIBE: Use localMqttClient.subscribe("exact_topic_string")
                    3. For PUBLISH: Use localMqttClient.publish("exact_topic_string", payload)
                    4. In callback functions: Use if (topicStr == "exact_topic_string")
                    5. DO NOT use placeholder topics like "///data" - use the exact strings provided above
                    6. JSON payload MUST include the key "{json_key}" as specified in the data point contract
                    7. When parsing subscribed messages, use ONLY the JSON keys specified in the payload schemas above
                    </CommunicationInstructions>
                    """)
                    print(f"  -> Generated communication context for '{device_role}':")
                    for instruction in comm_instructions:
                        print(f"     {instruction}")

            hardware_config_parts = []
            device_task = state.get('current_device_task')
            if device_task:
                for p in device_task.get('peripherals', []):
                    pins = p.get('pins', [])
                    if not pins and 'pin' in p:  # 兼容旧格式
                        pins = [{'name': 'PIN', 'number': p['pin']}]
                    if pins:
                        pin_details = ", ".join(
                            [f"{pin.get('name', 'PIN').upper()}: {pin.get('number', 'Not specified')}" for pin in pins])
                        hardware_config_parts.append(
                            f"- Peripheral '{p['name']}' ({p.get('model', 'N/A')}) uses pins: {pin_details}.")

            hardware_context = ""
            if hardware_config_parts:
                # 1. 先准备好内部的、未缩进的字符串内容
                content_inside = "\n".join(hardware_config_parts)
                # 2. 然后对这个准备好的字符串进行缩进
                indented_content = textwrap.indent(content_inside, '    ')
                # 3. 最后，将处理完毕的简单变量放入 f-string
                hardware_context = textwrap.dedent(f"""
                <HardwareConfiguration>
    {indented_content}
                </HardwareConfiguration>
                """)

            driver_headers = ""
            completed = state.get('completed_modules', {})
            dependencies = task.get('dependencies', [])

            for dep_id in dependencies:
                if dep_id in completed and completed[dep_id].get('header_code'):
                    driver_headers += f"--- Interface for {dep_id} from '{dep_id}.h' ---\\n```cpp\\n{completed[dep_id]['header_code']}\\n```\\n\\n"

            # V2.0 新增：获取并注入DP契约
            dp_contract = state.get("device_dp_contract", [])
            dp_contract_context = ""
            if dp_contract:
                dp_contract_context = textwrap.dedent(f"""
                <DataPointContract>
                {json.dumps(dp_contract, indent=2, ensure_ascii=False)}
                </DataPointContract>
                """)

            # V4 新增：获取任务契约并生成禁止性指令（第三层防御）
            task_contract = device_task.get('task_contract', {}) if device_task else {}
            contract_constraints = ""
            if task_contract:
                data_source = task_contract.get('data_source', 'local')
                forbidden_apis = task_contract.get('forbidden_hardware_apis', [])
                allowed_apis = task_contract.get('allowed_hardware_apis', [])

                if data_source == 'remote' and forbidden_apis:
                    contract_constraints = textwrap.dedent(f"""
                    <TaskContractConstraints>
                    **CRITICAL CONSTRAINT - REMOTE DATA SOURCE DEVICE:**
                    This device is configured as a DATA CONSUMER, not a data producer.

                    **STRICTLY FORBIDDEN APIs (DO NOT USE):**
                    {', '.join(forbidden_apis)}

                    **MANDATORY BEHAVIOR:**
                    - You MUST NOT call any local sensor reading functions
                    - You MUST NOT use analogRead() or any sensor driver APIs
                    - All data MUST come from subscribed MQTT messages only
                    - Parse received JSON data to extract sensor values
                    - Use the parsed values for control logic and decision making

                    **VIOLATION DETECTION:**
                    Any use of forbidden APIs will cause compilation failure and contract violation.
                    </TaskContractConstraints>
                    """)
                elif data_source == 'local' and allowed_apis:
                    contract_constraints = textwrap.dedent(f"""
                    <TaskContractConstraints>
                    **LOCAL DATA COLLECTION DEVICE:**
                    This device is configured for local sensor data collection.

                    **ALLOWED SENSOR APIs:**
                    {', '.join(allowed_apis)}

                    **MANDATORY BEHAVIOR:**
                    - Use only the allowed sensor APIs listed above
                    - Initialize sensors properly before reading
                    - Handle sensor reading errors appropriately
                    </TaskContractConstraints>
                    """)

                print(f"  -> [CONTRACT CONSTRAINTS] Generated for {data_source} data source")
                print(f"     Forbidden APIs: {forbidden_apis}")
                print(f"     Allowed APIs: {allowed_apis}")

            # Check if this is a Tuya device by multiple criteria
            is_tuya_device = ("tuya_handler" in dependencies or
                            any(keyword in task['description'].lower() for keyword in ['tuya', '涂鸦', '涂鸦云', '云平台', 'cloud platform']) or
                            state.get('cloud_product_id') is not None)

            # If Tuya device detected but tuya_handler not in dependencies, add it
            if is_tuya_device and "tuya_handler" not in dependencies:
                dependencies.append("tuya_handler")
                print("  -> CRITICAL: Detected Tuya cloud requirement, force-adding tuya_handler to dependencies")

            if is_tuya_device:
                print("  -> Generating DUAL-CLIENT (Tuya + Local) architecture for app_main.ino")
                prompt = textwrap.dedent(f"""
                    <Prompt>
                        {contract_reminder}

                        <Role>You are an expert embedded firmware developer for the ESP32.</Role>
                        <Goal>Generate the complete `app_main.ino` file for a dual-client MQTT application.</Goal>
                        <Architectural_Mandates>
                            1.  **Strict Task Focus**: Your ONLY task is to implement the COMPLETE logic described in `<TaskDescription>`. You MUST implement ALL business logic, conditional statements, thresholds, triggers, and actions specified in the description. Do NOT just collect and send data - implement the full functional behavior.
                            2.  **API Contract Mandate**: The header files provided in `<DriverInterfaces>` are the **absolute and only source of truth** for the driver APIs. You MUST call functions with the exact signatures provided, and use enum, macro, and struct names **exactly as they are defined** in those headers. Do not invent, guess, or use alternative names from your general knowledge if they conflict with the provided headers.
                            3.  **Implement ALL Communications**: The `<TaskDescription>` may contain instructions for both public cloud communication and local inter-device communication. Your code in `loop()` MUST implement **ALL** specified communication paths.
                            4.  **Complete Logic Implementation**: Implement ALL logic exactly as described in the TaskDescription, including all conditional statements, thresholds, and control flow specified.
                            5.  **Error Handling**: Before publishing sensor data, you MUST check if the reading is valid. For many sensors, a return value of -1 or less indicates an error. If an error is detected, you should log the error (e.g., `logger.println("Failed to read sensor.");`) and skip publishing for that cycle.
                            6.  **Data Point Contract Mandate (CRITICAL)**: You MUST implement the logic for all data points defined in the `<DataPointContract>` context below. The `code`, `type`, and `mode` specified in the contract are the absolute source of truth.

                            7.  **TuyaLink Payload Protocol**: When reporting data to Tuya, you MUST use the standard TuyaLink protocol. The JSON payload sent to `tuya_publish_data` MUST follow this exact structure:
                                - msgId: a unique string identifier
                                - time: current timestamp in milliseconds
                                - data: object containing DP codes as keys with value objects
                                - Example: {{"msgId": "unique_id", "time": 1678886400000, "data": {{"temperature": {{"value": 25.5}}, "humidity": {{"value": 60}}}}}}
                                - The data object's keys MUST be the exact `code` identifiers from the DataPointContract
                                - The data type of the value you send MUST match the type specified in the contract (e.g., if type is value, send an int; if float, send a float; if bool, send true/false).
                                - DO NOT use a properties wrapper inside the data object.
                            8.  **Include Headers**: You MUST include the necessary header files for all driver modules used, as detailed in `<DriverInterfaces>`.
                            9.  **Confirmation Logging**: Immediately after any successful `localMqttClient.publish()` or `tuya_publish_data()` call, you MUST add a log line confirming the action, for example: `logger.println("Published data to Tuya cloud.");`. This is mandatory for verification.
                            10. **Polling Interval**: The main `loop()` function MUST include a non-blocking delay mechanism to ensure it runs at a reasonable interval (e.g., every 5-10 seconds). Use a `static unsigned long ...` and `if (millis() - ... > ...)` pattern.
                            11. **Dual-Client Architecture & RTOS**: You MUST use two separate `PubSubClient` instances and a dedicated FreeRTOS task (`tuyaConnectionTask`) for the Tuya connection, as shown in the example.
                            12. **Hardware Initialization & Pin Types**: You MUST initialize all drivers using the pin numbers provided in the `<HardwareConfiguration>`. **Crucially, the data type used for the pin must match the function signature of the driver you are calling.**
                                - **CRITICAL GPIO ERROR PREVENTION**: Check the driver header file to determine the correct parameter type:
                                - If the driver function expects a `gpio_num_t` (common in ESP-IDF drivers), you MUST use the `GPIO_NUM_XX` format (e.g., `GPIO_NUM_23`).
                                - If the driver function expects a standard `int` or `uint8_t` (common in Arduino libraries), you MUST use the plain numeric value (e.g., `23`).
                                - **WRONG**: hcsr04_init(GPIO_NUM_23, GPIO_NUM_21) when driver expects int
                                - **CORRECT**: hcsr04_init(23, 21) when driver expects int
                                - **WRONG**: bh1750_init(0, 17, 4, false) when driver expects gpio_num_t for pins
                                - **CORRECT**: bh1750_init(I2C_NUM_0, GPIO_NUM_17, GPIO_NUM_4, false) when driver expects gpio_num_t
                            13. **Trigger Logic Understanding**: If the TaskDescription mentions "low-level trigger" or "high-level trigger", you MUST understand the electrical requirements correctly. For example, "low-level trigger buzzer" means the buzzer activates when the pin is LOW and deactivates when the pin is HIGH. The driver functions should implement the correct electrical logic.
                            14. **State Variable Separation**: For controllable devices (like buzzers, LEDs), you MUST separate control variables: one for enable/disable control (e.g., `buzzerEnabled`) and one for actual device state (e.g., `buzzerActive`). Always send the actual state to data points, not the enable state. Initialize enable variables to `true` for normal operation.
                            15. **Sensor Error Handling**: Always check for NAN/invalid sensor readings using `isnan()`. Convert NAN to a sentinel value (e.g., -1) before JSON serialization to prevent null values in data.
                            16. **BH1750 Sensor Initialization**: If using BH1750 light sensor, you MUST follow this exact sequence in setup():
                                - WRONG: bh1750_init(...); float lux = bh1750_read_lux(); (will fail)
                                - CORRECT: bh1750_init(...); bh1750_set_mode(BH1750_MODE_CONTINUOUS_HIGH_RES); delay(120); (then read in loop)
                                - The BH1750 requires measurement mode to be set after initialization before any readings
                                - Use continuous mode for repeated readings to avoid re-setting mode each time
                            17. **CRITICAL: Complete Sensor Data Collection**: You MUST read and publish data from ALL initialized sensors in the loop():
                                - If you call bh1750_init() in setup(), you MUST call bh1750_read_lux() in loop() and include it in JSON
                                - If you call hcsr04_init() in setup(), you MUST call hcsr04_read_distance_cm() in loop() and include it in JSON
                                - If you call dht22_init() in setup(), you MUST call dht22_read_temperature() and dht22_read_humidity() in loop()
                                - WRONG: Initialize sensor but don't read its data in loop()
                                - CORRECT: Every initialized sensor must have its data read and published
                            18. **CRITICAL: NAN Error Handling**: When checking for invalid sensor readings, you MUST include proper headers:
                                - ALWAYS include: #include <math.h> at the top of your file
                                - For Arduino/ESP32: Also include #include <Arduino.h> (provides NAN and isnan definitions)
                                - WRONG: if (isnan(value)) without proper includes - will cause compilation error
                                - CORRECT: #include <math.h> + #include <Arduino.h> then if (isnan(value)) value = -1.0f;
                            19. **State Variable Management**: If the TaskDescription implies the use of stateful values that need to be configured or tracked (e.g., "alarm threshold", "enable/disable switch", "mode setting"), you MUST declare corresponding global variables at the top of the file. These variables must be given reasonable default values.
                            20. **CRITICAL: Callback Dispatcher Pattern**: The `local_mqtt_callback` function MUST act as a central message dispatcher. It MUST use an `if-else if` structure to check the incoming `topicStr` and route the message to the correct processing logic. It is a critical error to process topics sequentially if one handler (like `ota_handle_mqtt_message`) might terminate the callback early.
                                - **Correct Pattern**:
                                ```cpp
                                void local_mqtt_callback(...) {{
                                    // ...
                                    if (topicStr == "app_topic_1") {{
                                        // handle data for topic 1
                                    }} else if (topicStr.startsWith("/ota/")) {{
                                        ota_handle_mqtt_message(...);
                                    }}
                                }}
                                ```
                                - **Wrong Pattern (to avoid)**:
                                ```cpp
                                void local_mqtt_callback(...) {{
                                    ota_handle_mqtt_message(...); // This might return early!
                                    if (topicStr == "app_topic_1") {{ ... }} // This line may never be reached.
                                }}
                                ```
                            21. **Direct Analog Read**: If the `<HardwareConfiguration>` context mentions a 'Generic Analog Sensor', you MUST implement its reading logic directly inside the `loop()` function using `analogRead(pin_number)`. Do not attempt to `#include` a separate driver for it.
                            22. **FreeRTOS Headers**: When using FreeRTOS functions like `xTaskCreate`, `vTaskDelay`, or `portTICK_PERIOD_MS`, you MUST explicitly include `#include "freertos/FreeRTOS.h"` and `#include "freertos/task.h"`. Do not rely on implicit inclusion through other libraries.
                            23. **CRITICAL: Communication Contract Compliance**: If a `<CommunicationContract>` is provided in the Context, you MUST follow it exactly:
                                - Use the EXACT topic strings specified for PUBLISH and SUBSCRIBE operations
                                - DO NOT use placeholder topics like "///data" or "/sensor/value"
                                - The topic strings in the contract are the absolute source of truth
                                - Example: If contract says "SUBSCRIBE to: /smart_home/sensor/data", use exactly that string

                            <Rule_ID>CRITICAL_DEPENDENCY_RULE_APP</Rule_ID>
                            <Rule_Description>
                            15. **MANDATORY Header Inclusion for Arduino**: You MUST ensure that for every standard library function, macro, or type you use, you include the corresponding header file. This is critical for compilation success:
                                - For Arduino core functions, you MUST `#include <Arduino.h>` at the very beginning.
                                - For mathematical functions like `sqrt`, `pow`, or the `NAN` macro, you MUST `#include <math.h>`.
                                - For string manipulation functions, you MUST `#include <string.h>`.
                                - For standard types like `uint8_t`, `uint16_t`, you MUST `#include <stdint.h>`.
                                - For time functions like `time()`, `localtime()`, you MUST `#include "time.h"`.
                                - For FreeRTOS functions like `xTaskCreate`, `vTaskDelay`, you MUST `#include "freertos/FreeRTOS.h"` and `#include "freertos/task.h"`.
                                - Always include Arduino.h FIRST, before any other includes, to establish the proper compilation environment.
                            16. **Header Order**: Always include `#include <Arduino.h>` as the FIRST include statement to prevent compilation errors with other libraries.
                            </Rule_Description>
                        </Architectural_Mandates>

                        <PayloadContract>
                            **🚨🚨🚨 CRITICAL MQTT PAYLOAD CONTRACT (MANDATORY) 🚨🚨🚨**

                            ⚠️ AUTOMATIC VALIDATION: This code will be scanned by automated contract validation. ANY deviation will cause IMMEDIATE REJECTION and force regeneration.

                            **🎯 EXACT CONTRACT REQUIREMENTS (NO EXCEPTIONS):**

                            **JSON KEY REQUIREMENT:**
                            - You MUST use EXACTLY this JSON key: "{payload_contract['json_key']}"
                            - NO variations, NO alternatives, NO creative interpretations
                            - The validation system will search for: doc["{payload_contract['json_key']}"]

                            **MQTT TOPIC REQUIREMENT:**
                            - You MUST publish to EXACTLY this topic: "{payload_contract['topic']}"
                            - NO variations, NO alternatives

                            **🔥 CRITICAL: Your code MUST contain these EXACT lines:**
                            ```cpp
                            doc["{payload_contract['json_key']}"] = sensor_value;
                            serializeJson(doc, jsonString);
                            localMqttClient.publish("{payload_contract['topic']}", jsonString.c_str());
                            ```

                            **❌ THESE WILL CAUSE IMMEDIATE REJECTION:**
                            - doc["lux"] = value;
                            - doc["value"] = value;
                            - doc["illuminance"] = value; (unless this is the exact required key)
                            - doc["light"] = value;
                            - doc["sensor_data"] = value;
                            - Any JSON key other than "{payload_contract['json_key']}"

                            **🎯 REMEMBER: The required JSON key is "{payload_contract['json_key']}" - use it EXACTLY as shown!**
                        </PayloadContract>

                        <Context>
                            <TaskDescription>{task['description']}</TaskDescription>
                            {hardware_context}
                            <DriverInterfaces>{driver_headers}</DriverInterfaces>
                            {dp_contract_context}
                            {communication_context}
                            {contract_constraints}
                            <IsTuyaDevice>{is_tuya_device}</IsTuyaDevice>
                            {feedback_context}
                        </Context>
                        <CorrectExample_for_Tuya_Dual_Client_Architecture>
                        ```cpp
                                // --- Arduino 核心环境 ---
                                #include <Arduino.h>

                                // --- 核心库 ---
                                #include <WiFi.h>
                                #include <PubSubClient.h>
                                #include <ArduinoJson.h>
                                #include <WiFiClientSecure.h>
                                #include "time.h"
                                #include "freertos/FreeRTOS.h"
                                #include "freertos/task.h"
            
                                // --- 项目中的功能模块库 ---
                                #include "config_manager.h"
                                #include "tuya_handler.h"
                                #include "ota_handler.h"
                                #include "mqtt_logger.h"
                                #include <HTTPUpdate.h>
                                // #include "bh1750_driver.h" // EXAMPLE DRIVER - You must include the actual drivers you use.
            
                                // =======================================================================
                                // 1. 双客户端定义
                                // =======================================================================
                                WiFiClientSecure tuyaWifiClient;
                                PubSubClient tuyaMqttClient(tuyaWifiClient);
                                WiFiClient localWifiClient;
                                HTTPUpdate myHttpUpdate;
                                PubSubClient localMqttClient(localWifiClient);
                                Print& logger = MqttLogger::getInstance();
            
                                // =======================================================================
                                // 2. 回调函数定义
                                // =======================================================================
                                void handle_tuya_app_commands(String &topic, String &payload) {{
                                    logger.println("Received application command from Tuya Cloud via handler.");
                                    // Add logic here to handle commands like `enable_report`
                                }}
            
                                void tuya_mqtt_callback(char* topic, byte* payload, unsigned int length) {{
                                    tuya_handle_mqtt_message(topic, payload, length);
                                }}
            
                                void local_mqtt_callback(char* topic, byte* payload, unsigned int length) {{
                                    String topicStr = String(topic);
                                    String payloadStr;
                                    payloadStr.reserve(length);
                                    for (unsigned int i = 0; i < length; i++) {{ payloadStr += (char)payload[i]; }}
                                    ota_handle_mqtt_message(topicStr, payloadStr);
                                }}
            
                                // =======================================================================
                                // 3. 连接函数
                                // =======================================================================
                                void connectToTuya() {{
                                    logger.println("Attempting to connect to TUYA MQTT Broker...");
                                    char clientId[128], username[256], password[128];
                                    tuya_get_mqtt_credentials(clientId, username, password);
                                    if (tuyaMqttClient.connect(clientId, username, password)) {{
                                        logger.println("SUCCESS: Connected to Tuya MQTT Broker.");
                                        tuya_subscribe_topics();
                                    }} else {{
                                        logger.printf("FAILED, Tuya client state=%d.\\n", tuyaMqttClient.state());
                                    }}
                                }}
            
                                void tuyaConnectionTask(void *pvParameters) {{
                                    logger.println("Tuya Connection Task started.");
                                    for (;;) {{
                                        if (!tuyaMqttClient.connected()) {{
                                            connectToTuya();
                                            if (!tuyaMqttClient.connected()) {{
                                                vTaskDelay(5000 / portTICK_PERIOD_MS);
                                            }}
                                        }}
                                        tuyaMqttClient.loop();
                                        vTaskDelay(20 / portTICK_PERIOD_MS);
                                    }}
                                }}
            
                                void connectToLocalMqtt() {{
                                    while (!localMqttClient.connected()) {{
                                        logger.println("Attempting to connect to LOCAL MQTT Broker...");
                                        if (localMqttClient.connect(DEVICE_ID)) {{
                                            logger.println("SUCCESS: Connected to Local MQTT Broker.");
                                            ota_init(localWifiClient, localMqttClient, myHttpUpdate);
                                        }} else {{
                                            logger.printf("FAILED, rc=%d. Retrying in 5 seconds\\n", localMqttClient.state());
                                            delay(5000);
                                        }}
                                    }}
                                }}
            
                                // =======================================================================
                                // 4. 主程序: Setup & Loop
                                // =======================================================================
                                void setup() {{
                                    Serial.begin(115200);
                                    delay(10);
                                    Serial.println("\\n--- Dual-Client Firmware with Dedicated Task ---");
                                    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
                                    while (WiFi.status() != WL_CONNECTED) {{ delay(500); Serial.print("."); }}
                                    Serial.println("\\nWiFi connected.");
                                    configTime(0, 0, "pool.ntp.org", "time.nist.gov");
                                    time_t now = time(NULL);
                                    while (now < 8 * 3600 * 2) {{ delay(500); now = time(NULL); }}
                                    MqttLogger::getInstance().begin(localMqttClient, DEVICE_ID);
                                    logger.println("--- System Initializing ---");
            
                                    // Initialize handlers
                                    tuya_init(tuyaWifiClient, tuyaMqttClient, handle_tuya_app_commands);
            
                                    // Configure MQTT clients
                                    tuyaMqttClient.setServer("m1.tuyacn.com", 8883);
                                    tuyaMqttClient.setCallback(tuya_mqtt_callback);
                                    localMqttClient.setServer(MQTT_BROKER, MQTT_PORT);
                                    localMqttClient.setCallback(local_mqtt_callback);
            
                                    // Connect to local broker and initialize OTA
                                    connectToLocalMqtt();
            
                                    // Create Tuya connection task
                                    xTaskCreate(tuyaConnectionTask, "TuyaTask", 10240, NULL, 1, NULL);
            
                                    logger.println("--- Setup Complete, Main Loop is starting ---");
                                }}
                                void loop() {{
                                    if (!localMqttClient.connected()) {{ connectToLocalMqtt(); }}
                                    localMqttClient.loop();
                                    MqttLogger::getInstance().loop();
                                    // Add application logic here, e.g., reading sensors and publishing data.
                                }}
                                ```
                                </CorrectExample_for_Tuya_Dual_Client_Architecture>
                                <OutputFormat>You MUST provide a single, complete code block for the main `.ino` file. Your code must strictly follow all rules and the style of the provided example.</OutputFormat>
                            </Prompt>
                        """)
            else:
                print("  -> Generating SINGLE-CLIENT (Local only) architecture for app_main.ino")
                prompt = textwrap.dedent(f"""
                                <Prompt>
                                    {contract_reminder}

                                    <Role>You are an expert embedded firmware developer creating a robust IoT application for the ESP32.</Role>
                                    <Goal>Your primary task is to generate the *entire* `app_main.ino` file. The firmware only needs to connect to a **local MQTT broker** for debugging and OTA updates.</Goal>
                                    <Architectural_Mandates>
                                        1.  **Strict Task Focus**: Your ONLY task is to implement the COMPLETE logic described in `<TaskDescription>`. You MUST implement ALL business logic, conditional statements, thresholds, triggers, and actions specified in the description. Do NOT just collect and send data - implement the full functional behavior.
                                        2.  **API Contract Mandate**: The header files provided in `<DriverInterfaces>` are the **absolute and only source of truth** for the driver APIs. You MUST call functions with the exact signatures provided, and use enum, macro, and struct names **exactly as they are defined** in those headers. Do not invent, guess, or use alternative names from your general knowledge if they conflict with the provided headers.
                                        3.  **Complete Logic Implementation**: Implement ALL logic exactly as described in the TaskDescription, including all conditional statements, thresholds, and control flow specified.
                                        4.  **Include Headers**: You MUST include the necessary header files for all driver modules used, as detailed in `<DriverInterfaces>`.
                                        5.  **Confirmation Logging**: Immediately after any successful `localMqttClient.publish()` call, you MUST add a log line confirming the action, for example: `logger.println("Published data to local MQTT.");`. This is mandatory for verification.
                                        6.  **Polling Interval**: The main `loop()` function MUST include a non-blocking delay mechanism to ensure it runs at a reasonable interval (e.g., every 5-10 seconds). Use a `static unsigned long lastActionTime = 0;` and `if (millis() - lastActionTime > 5000) {{ ... }}` pattern.
                                        7.  **Single-Client Architecture**: You MUST define only ONE `PubSubClient` instance: `localMqttClient` (using `WiFiClient`).
                                        8.  **Hardware Initialization & Pin Types**: You MUST initialize all drivers using the pin numbers provided in the `<HardwareConfiguration>`. **Crucially, the data type used for the pin must match the function signature of the driver you are calling.**
                                            - If the driver function expects a `gpio_num_t` (common in ESP-IDF drivers), you MUST use the `GPIO_NUM_XX` format (e.g., `GPIO_NUM_23`).
                                            - If the driver function expects a standard `int` or `uint8_t` (common in Arduino libraries), you MUST use the plain numeric value (e.g., `23`).
                                        9.  **Trigger Logic Understanding**: If the TaskDescription mentions "low-level trigger" or "high-level trigger", you MUST understand the electrical requirements correctly. For example, "low-level trigger buzzer" means the buzzer activates when the pin is LOW and deactivates when the pin is HIGH. The driver functions should implement the correct electrical logic.
                                        10. **State Variable Separation**: For controllable devices (like buzzers, LEDs), you MUST separate control variables: one for enable/disable control (e.g., `buzzerEnabled`) and one for actual device state (e.g., `buzzerActive`). Always send the actual state to data points, not the enable state. Initialize enable variables to `true` for normal operation.
                                        11. **Sensor Error Handling**: Always check for NAN/invalid sensor readings using `isnan()`. Convert NAN to a sentinel value (e.g., -1) before JSON serialization to prevent null values in data.
                                        12. **State Variable Management**: If the TaskDescription implies the use of stateful values that need to be configured or tracked (e.g., "alarm threshold", "enable/disable switch", "mode setting"), you MUST declare corresponding global variables at the top of the file. These variables must be given reasonable default values.
                                        13. **CRITICAL: Callback Dispatcher Pattern**: The `local_mqtt_callback` function MUST act as a central message dispatcher. It MUST use an `if-else if` structure to check the incoming `topicStr` and route the message to the correct processing logic. It is a critical error to process topics sequentially if one handler (like `ota_handle_mqtt_message`) might terminate the callback early.
                                            - **Correct Pattern**:
                                            ```cpp
                                            void local_mqtt_callback(...) {{
                                                // ...
                                                if (topicStr == "app_topic_1") {{
                                                    // handle data for topic 1
                                                }} else if (topicStr.startsWith("/ota/")) {{
                                                    ota_handle_mqtt_message(...);
                                                }}
                                            }}
                                            ```
                                            - **Wrong Pattern (to avoid)**:
                                            ```cpp
                                            void local_mqtt_callback(...) {{
                                                ota_handle_mqtt_message(...); // This might return early!
                                                if (topicStr == "app_topic_1") {{ ... }} // This line may never be reached.
                                            }}
                                            ```
                                        14. **Direct Analog Read**: If the `<HardwareConfiguration>` context mentions a 'Generic Analog Sensor', you MUST implement its reading logic directly inside the `loop()` function using `analogRead(pin_number)`. Do not attempt to `#include` a separate driver for it.

                                        <Rule_ID>CRITICAL_DEPENDENCY_RULE_SINGLE_CLIENT</Rule_ID>
                                        <Rule_Description>
                                        10. **MANDATORY Header Inclusion for Arduino**: You MUST ensure that for every standard library function, macro, or type you use, you include the corresponding header file. This is critical for compilation success:
                                            - For Arduino core functions, you MUST `#include <Arduino.h>` at the very beginning.
                                            - For mathematical functions like `sqrt`, `pow`, or the `NAN` macro, you MUST `#include <math.h>`.
                                            - For string manipulation functions, you MUST `#include <string.h>`.
                                            - For standard types like `uint8_t`, `uint16_t`, you MUST `#include <stdint.h>`.
                                            - For time functions like `time()`, `localtime()`, you MUST `#include "time.h"`.
                                            - Always include Arduino.h FIRST, before any other includes, to establish the proper compilation environment.
                                        11. **Header Order**: Always include `#include <Arduino.h>` as the FIRST include statement to prevent compilation errors with other libraries.
                                        </Rule_Description>
                                    </Architectural_Mandates>

                                    <PayloadContract>
                                        **🚨🚨🚨 CRITICAL MQTT PAYLOAD CONTRACT (MANDATORY) 🚨🚨🚨**

                                        ⚠️ AUTOMATIC VALIDATION: This code will be scanned by automated contract validation. ANY deviation will cause IMMEDIATE REJECTION and force regeneration.

                                        **🎯 EXACT CONTRACT REQUIREMENTS (NO EXCEPTIONS):**

                                        **JSON KEY REQUIREMENT:**
                                        - You MUST use EXACTLY this JSON key: "{payload_contract['json_key']}"
                                        - NO variations, NO alternatives, NO creative interpretations
                                        - The validation system will search for: doc["{payload_contract['json_key']}"]

                                        **MQTT TOPIC REQUIREMENT:**
                                        - You MUST publish to EXACTLY this topic: "{payload_contract['topic']}"
                                        - NO variations, NO alternatives

                                        **🔥 CRITICAL: Your code MUST contain these EXACT lines:**
                                        ```cpp
                                        doc["{payload_contract['json_key']}"] = sensor_value;
                                        serializeJson(doc, jsonString);
                                        localMqttClient.publish("{payload_contract['topic']}", jsonString.c_str());
                                        ```

                                        **❌ THESE WILL CAUSE IMMEDIATE REJECTION:**
                                        - doc["lux"] = value;
                                        - doc["value"] = value;
                                        - doc["illuminance"] = value; (unless this is the exact required key)
                                        - doc["light"] = value;
                                        - doc["sensor_data"] = value;
                                        - Any JSON key other than "{payload_contract['json_key']}"

                                        **🎯 REMEMBER: The required JSON key is "{payload_contract['json_key']}" - use it EXACTLY as shown!**
                                    </PayloadContract>

                                    <Context>
                                        <TaskDescription>{task['description']}</TaskDescription>
                                        {hardware_context}
                                        <DriverInterfaces>{driver_headers}</DriverInterfaces>
                                        {communication_context}
                                        {contract_constraints}
                                        <IsTuyaDevice>{is_tuya_device}</IsTuyaDevice>
                                        {feedback_context}
                                    </Context>
                                    <CorrectExample_for_Single_Client_Architecture>
                                    ```cpp
                                    // --- Arduino Core Environment ---
                                    #include <Arduino.h>

                                    // --- Core Libraries ---
                                    #include <WiFi.h>
                                    #include <PubSubClient.h>
                                    #include <ArduinoJson.h>
                                    #include <HTTPUpdate.h>
                
                                    // --- Project Modules ---
                                    #include "config_manager.h"
                                    #include "ota_handler.h"
                                    #include "mqtt_logger.h"
                                    // #include "bh1750_driver.h" // EXAMPLE - You must include the actual drivers you use!
                
                                    // --- Client Definitions ---
                                    WiFiClient localWifiClient;
                                    HTTPUpdate myHttpUpdate;
                                    PubSubClient localMqttClient(localWifiClient);
                                    Print& logger = MqttLogger::getInstance();
                
                                    // --- Callback ---
                                    void local_mqtt_callback(char* topic, byte* payload, unsigned int length) {{
                                        String topicStr = String(topic);
                                        String payloadStr;
                                        payloadStr.reserve(length);
                                        for (unsigned int i = 0; i < length; i++) {{ payloadStr += (char)payload[i]; }}
                                        ota_handle_mqtt_message(topicStr, payloadStr);
                                        // Add other command handling here if needed...
                                    }}
                
                                    // --- Connection ---
                                    void connectToLocalMqtt() {{
                                        while (!localMqttClient.connected()) {{
                                            logger.println("Attempting to connect to LOCAL MQTT Broker...");
                                            if (localMqttClient.connect(DEVICE_ID)) {{
                                                logger.println("SUCCESS: Connected to Local MQTT Broker.");
                                                // Initialize OTA after a successful connection
                                                ota_init(localWifiClient, localMqttClient, myHttpUpdate);
                                            }} else {{
                                                logger.printf("FAILED, rc=%d. Retrying in 5 seconds\\n", localMqttClient.state());
                                                delay(5000);
                                            }}
                                        }}
                                    }}
                
                                    // --- Main Program ---
                                    void setup() {{
                                        Serial.begin(115200);
                                        delay(10);
                                        WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
                                        while (WiFi.status() != WL_CONNECTED) {{ delay(500); Serial.print("."); }}
                                        Serial.println("\\nWiFi connected.");
                
                                        // Initialize logger after WiFi
                                        MqttLogger::getInstance().begin(localMqttClient, DEVICE_ID);
                                        logger.println("--- System Initializing (Local Mode) ---");
                
                                        // Configure and connect to local MQTT
                                        localMqttClient.setServer(MQTT_BROKER, MQTT_PORT);
                                        localMqttClient.setCallback(local_mqtt_callback);
                                        connectToLocalMqtt();
                
                                        // Initialize your drivers here
                                        // bh1750_setup(34); // Example driver setup
                
                                        logger.println("--- Setup Complete ---");
                                    }}
                
                                    void loop() {{
                                        if (!localMqttClient.connected()) {{ connectToLocalMqtt(); }}
                                        localMqttClient.loop();
                                        MqttLogger::getInstance().loop();
                
                                        // Correct non-blocking timer for periodic actions
                                        static unsigned long lastPublishTime = 0;
                                        if (millis() - lastPublishTime > 5000) {{ // 5-second interval
                                            lastPublishTime = millis();
                                            
                                            // CRITICAL: Read ALL initialized sensors and publish their data
                                            JsonDocument doc;

                                            // Read BH1750 light sensor (if initialized)
                                            float lux = bh1750_read_lux();
                                            if (isnan(lux)) lux = -1.0f;
                                            doc["lux"] = lux;

                                            // Read HC-SR04 distance sensor (if initialized)
                                            float distance = hcsr04_read_distance_cm();
                                            if (isnan(distance)) distance = -1.0f;
                                            doc["distance"] = distance;

                                            // WRONG: Only reading one sensor when multiple are initialized
                                            // doc["lux"] = bh1750_read_lux(); // Missing distance data!

                                            char buffer[128];
                                            serializeJson(doc, buffer);
                                            if (localMqttClient.publish("/data/local/report", buffer)) {{
                                                logger.printf("Published complete sensor data: %s\\n", buffer);
                                            }}
                                        }}
                                    }}
                                    ```
                                    </CorrectExample_for_Single_Client_Architecture>
                                    <OutputFormat>You MUST provide a single, complete code block for the main `.ino` file based on the single-client architecture.</OutputFormat>
                                </Prompt>
                                """)
            response = developer_model.invoke([HumanMessage(content=prompt)])
            main_code = extract_code(response.content, lang="cpp")

            # 【四层防御体系】验证生成的代码是否符合所有契约
            contract_feedback = ""
            if task_id == 'app_main':
                violations = []

                # 第零层检查：通信契约合规性（新增）
                try:
                    _assert_topic_compliance(main_code or "", unified_contract)
                    print("  -> ✅ Communication contract compliance check passed")
                except ValueError as e:
                    violations.append(f"Communication Contract: {str(e)}")
                    print(f"  -> ❌ Communication contract violation: {e}")

                # 第一层检查：JSON载荷契约（正向契约）
                json_violation = _violates_json_contract(main_code or "", payload_contract)
                if json_violation:
                    violations.append(f"JSON Contract: {json_violation}")

                # 第四层检查：任务契约（正向+反向契约）
                device_task = state.get('current_device_task', {})
                task_contract = device_task.get('task_contract', {}) if device_task else {}
                if task_contract:
                    task_violation = _validate_task_contract(main_code or "", task_contract)
                    if task_violation:
                        violations.append(f"Task Contract: {task_violation}")
                        print(f"  -> 🚫 CRITICAL: Task contract violation detected!")
                        print(f"     Device Role: {device_task.get('device_role', 'Unknown') if device_task else 'Unknown'}")
                        print(f"     Data Source: {task_contract.get('data_source', 'Unknown')}")
                        print(f"     Violation: {task_violation}")

                # 新增：通信契约检查（第四层防御扩展）
                unified_contract = state.get('unified_communication_contract', {})
                device_role = device_task.get('device_role', '') if device_task else ''
                if unified_contract and device_role:
                    comm_violation = _validate_communication_contract(main_code or "", unified_contract, device_role)
                    if comm_violation:
                        violations.append(f"Communication Contract: {comm_violation}")
                        print(f"  -> 🚫 CRITICAL: Communication contract violation detected!")
                        print(f"     Device Role: {device_role}")
                        print(f"     Violation: {comm_violation}")

                if violations:
                    contract_feedback = f"FAIL: Contract violations detected — {'; '.join(violations)}"
                    print(f"  -> ⚠️ Contract violations detected: {len(violations)} issues")
                    for i, violation in enumerate(violations, 1):
                        print(f"     {i}. {violation}")
                    print(f"  -> 🔄 Regenerating due to contract violation(s).")

                    # 🔧 CRITICAL FIX: 任何契约违规都必须阻断保存流程并反馈 FAIL
                    # 无论是 Task Contract, Communication Contract, JSON Contract 等都需要重新生成
                    return {"feedback": contract_feedback, "current_module_task": task}
                else:
                    print(f"  -> ✅ All contract validations passed")
                    print(f"     - JSON Contract: ✓")
                    print(f"     - Task Contract: ✓")

            # V4.0 CONTRACT-ENHANCED: 只有通过所有契约检查的代码才会被保存
            completed_modules[task_id] = {"task_id": task_id, "header_code": None, "source_code": None,
                                        "main_code": main_code, "version": version}

        # 添加完成开发的智能日志
        if task.get('task_type') == 'driver':
            _log_with_personality(workflow_id, f"完成了 {task_id} 驱动的开发，包含头文件和实现文件", "success")
        elif task_id == 'app_main':
            _log_with_personality(workflow_id, f"主程序开发完成！所有模块已整合到 app_main.ino 中", "success")
        else:
            _log_with_personality(workflow_id, f"完成了 {task_id} 模块的开发", "success")

        # 确保契约信息传递到下一个节点
        result = {"completed_modules": completed_modules, "feedback": contract_feedback, "current_module_task": task}
        if task_id == 'app_main' and payload_contract:
            result["payload_contract"] = payload_contract

        return result


    def integrator_node(state: AgentState) -> Dict:
        current_device_task = state.get('current_device_task')
        if not current_device_task:
            return {"feedback": "FAIL: No current device task found"}

        device_id = current_device_task['internal_device_id']
        device_role = current_device_task.get('device_role', device_id)
        print(f"--- L6: INTEGRATOR: Assembling final verified firmware for '{device_id}' ---")

        # 使用辅助函数生成简短的环境名
        folder_name, env_name = generate_short_names(device_id, device_role)
        print(f"  -> Using PlatformIO environment name: '{env_name}'")

        project_files = state.get('project_files', {})
        # 🔧 CRITICAL: 确保 project_files 是字典而不是 None
        if project_files is None:
            project_files = {}
        final_project_files = {}
        completed_modules = state.get('completed_modules', {})
        final_project_files["lib/"] = ""
        final_project_files["src/"] = ""
        for task_id, module in completed_modules.items():
            # 规范化文件名，确保不包含中文字符
            safe_task_id = sanitize_filename(task_id)

            if task_id == 'tuya_handler':
                module_dir = f"lib/{safe_task_id}/"
                final_project_files[module_dir] = ""
                if module.get('header_code'): final_project_files[f"{module_dir}{safe_task_id}.h"] = module['header_code']
                if module.get('source_code'):
                    try:
                        source_files = json.loads(module['source_code'])
                        for filename, content in source_files.items():
                            # 也规范化源文件中的文件名
                            safe_filename = sanitize_filename(filename.replace('.h', '').replace('.cpp', '')) + ('.' + filename.split('.')[-1] if '.' in filename else '')
                            final_project_files[f"{module_dir}{safe_filename}"] = content
                    except (json.JSONDecodeError, TypeError):
                        final_project_files[f"{module_dir}{safe_task_id}.cpp"] = module['source_code']
                continue
            if task_id in ['config_manager', 'ota_handler', 'mqtt_logger']:
                module_dir = f"lib/{safe_task_id}/"
                final_project_files[module_dir] = ""
                if module.get('header_code'): final_project_files[f"{module_dir}{safe_task_id}.h"] = module['header_code']
                if module.get('source_code'): final_project_files[f"{module_dir}{safe_task_id}.cpp"] = module['source_code']
            else:
                if module.get('header_code'): final_project_files[f"src/{safe_task_id}.h"] = module['header_code']
                if module.get('source_code'): final_project_files[f"src/{safe_task_id}.cpp"] = module['source_code']
                if module.get('main_code'):
                    # 🛡️ SAFETY CHECK: 最后一道防线，确保没有占位符主题遗留
                    main_code = module['main_code']
                    # 只检查明显的占位符，不检查可能的合法topic
                    forbidden_topics = ['///data', '/sensor/value']
                    for forbidden_topic in forbidden_topics:
                        if f'"{forbidden_topic}"' in main_code:
                            print(f"  -> 🚨 CRITICAL: Found forbidden placeholder topic '{forbidden_topic}' in {task_id}")
                            print(f"  -> 🔄 This should have been caught earlier by contract validation!")
                            return {"feedback": f"FAIL: Integrator safety check failed - found placeholder topic '{forbidden_topic}' in final code"}
                    final_project_files[f"src/{task_id}.ino"] = main_code

        user_board_model = current_device_task['board']
        corrected_board_id = find_board_id(user_board_model) or "esp32dev"

        # 注入 SCons 缓存配置脚本
        # 脚本路径计算逻辑：从 .../temp_workspaces/<workflow_id>/<folder_name> 退两级到 temp_workspaces/
        scons_cache_script = """
    import os
    from os.path import abspath, join
    Import("env")

    # 定位到共享的 temp_workspaces 目录
    root_path = abspath(join(env["PROJECT_DIR"], "..", ".."))
    cache_dir = abspath(join(root_path, ".build_cache"))
    os.makedirs(cache_dir, exist_ok=True)

    print(f"--- [SCons Caching] Using shared cache directory: {cache_dir} ---")
    env.CacheDir(cache_dir)
    """
        # 将脚本添加到待写入的文件字典中
        final_project_files["scripts/"] = "" # 确保 scripts 目录被创建
        final_project_files["scripts/scons_cache.py"] = scons_cache_script

        # 修改 platformio.ini 模板以使用该脚本
        final_project_files["platformio.ini"] = f"""
    [platformio]
    ; build_cache_dir 在 6.1.18 不生效，已移除。改用 extra_scripts 启用 SCons CacheDir。
    build_dir = .bld
    libdeps_dir = .ldp
    default_envs = {env_name}

    [env:{env_name}]
    platform = espressif32
    board = {corrected_board_id}
    framework = arduino
    lib_deps =
        knolleary/PubSubClient
        bblanchon/ArduinoJson
    monitor_speed = 115200
    lib_archive = false
    ; lib_extra_dirs = lib/  <-- 已移除此冗余行
    extra_scripts = pre:scripts/scons_cache.py
    """
        project_files[device_id] = final_project_files
        return {"project_files": project_files}

    def test_plan_designer_node(state: AgentState) -> Dict:
        print("--- L4: TEST PLAN DESIGNER V5: Creating test plan from Unified Communication Contract ---")

        unified_contract = state.get("unified_communication_contract")
        current_device_task = state.get("current_device_task", {})
        device_role = current_device_task.get('device_role', '')
        dp_contract = state.get("device_dp_contract", [])

        if not (unified_contract and device_role and dp_contract):
            print("  -> ERROR: Missing contracts for test plan generation. Skipping.")
            return {"test_plan": None}

        # 1. 从统一通信契约获取 Topic (唯一真相来源)
        topic_map = unified_contract.get('topic_map', {})
        device_topics = topic_map.get(device_role, {"pub": [], "sub": []})

        # 测试计划应该监听设备"发布"的主题。对于报警器，它发布的是报警信息。
        # 对于传感器，它发布的是传感器数据。
        topic_to_monitor = device_topics["pub"][0] if device_topics["pub"] else (device_topics["sub"][0] if device_topics["sub"] else None)

        if not topic_to_monitor:
            print(f"  -> WARNING: No publish/subscribe topic found for '{device_role}'. Cannot create test plan.")
            return {"test_plan": None}

        # 2. 从 DP 契约获取 Key (唯一真相来源)
        primary_dp = dp_contract[0]
        json_key_to_check = primary_dp.get('code')
        if not json_key_to_check:
            print("  -> ERROR: First DP in contract is missing a 'code'.")
            return {"test_plan": None}

        # 3. 构建测试计划
        plan = {
            "device_log_topic": topic_to_monitor,
            "sequence": [{
                "name": f"Check for {json_key_to_check} data publication",
                "expected_log_contains": f'"{json_key_to_check}":',
                "timeout_seconds": 30
            }],
            "success_criteria": "ALL_PASS"
        }

        print(f"  -> Generated test plan from unified contract: topic='{plan['device_log_topic']}', expecting key='{json_key_to_check}'")

        # 保持 payload_contract 供其他可能需要的节点使用
        payload_contract = {"topic": topic_to_monitor, "json_key": json_key_to_check}

        return {"test_plan": plan, "payload_contract": payload_contract}


    def prepare_workspace_node(state: AgentState) -> Dict:
        """
        为当前设备创建一个隔离的子目录，并将所有项目文件写入其中。
        使用简短但可读的命名方式避免 Windows 路径长度限制。
        """
        device_task = state.get('current_device_task')
        if not device_task:
            return {"feedback": "FAIL: No current device task found"}

        device_id = device_task['internal_device_id']
        device_role = device_task.get('device_role', device_id)

        # 使用辅助函数生成简短的名称
        folder_name, env_name = generate_short_names(device_id, device_role)

        print(f"\n--- PREPARING WORKSPACE: Creating directory for device '{device_role}' -> '{folder_name}' ---")
        print(f"  -> Device ID: {device_id} -> Environment: {env_name}")

        # 🔧 检查项目文件是否存在（可能因为integrator失败而缺失）
        project_files_dict = state.get('project_files', {})
        if device_id not in project_files_dict:
            print(f"  -> ❌ ERROR: No project files found for device {device_id}")
            print(f"  -> 🔄 This usually means the integrator_node failed earlier")
            return {"feedback": f"FAIL: No project files available for device {device_id} - integration may have failed"}

        project_files = project_files_dict[device_id]
        base_workspace_path = Path(state['workspace_path'])
        device_project_path = base_workspace_path / folder_name

        # 清理旧目录（如果存在），确保每次都是全新的开始
        if device_project_path.exists():
            robust_rmtree(device_project_path)
        device_project_path.mkdir(parents=True, exist_ok=True)

        print(f"  -> Writing project files to: '{device_project_path}'")

        for filename, content in project_files.items():
            dest_path = device_project_path / filename
            if filename.endswith('/'):
                dest_path.mkdir(parents=True, exist_ok=True)
                continue
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            dest_path.write_text(content, encoding="utf-8")

        # 将准备好的、设备专用的路径作为 build_dir 返回，供后续节点使用
        return {"build_dir": str(device_project_path)}


    def deployment_and_verification_node(state: AgentState) -> Dict:
        device_task = state.get('current_device_task')
        if not device_task:
            return {"feedback": "FAIL: No current device task found"}

        device_id = device_task['internal_device_id']
        device_role = device_task.get('device_role', device_id)
        build_dir_path = state.get("build_dir")
        if not build_dir_path:
            print(f"  -> ❌ ERROR: No build directory found in state")
            print(f"  -> 🔄 This usually means the prepare_workspace_node failed earlier")
            return {"feedback": "FAIL: No build directory available - workspace preparation may have failed"}

        build_dir = Path(build_dir_path)  # 直接从state获取已准备好的路径

        if not build_dir or not build_dir.exists():
            return {"feedback": f"FAIL: Build directory for device '{device_role}' not found."}

        print(f"\n--- L6: DEPLOYMENT & VERIFICATION: Preparing verification for '{device_role}' ---")
        print(f"  -> Using prepared project directory: '{build_dir}'")

        test_plan = state.get('test_plan')

        if not test_plan or not isinstance(test_plan, dict) or not test_plan.get("sequence"):
            print("  -> Verification skipped (no test plan or sequence found).")
            return {"feedback": "PASS: Verification skipped (no test plan generated)."}

        local_pc_ip = get_local_ip()
        raw_topic = test_plan.get("device_log_topic", f"/debug/{device_id}/log")
        topic_to_verify = raw_topic.format(DEVICE_ID=device_id)
        topic_to_verify = topic_to_verify.replace("DEVICE_ID", device_id)
        print(f"  -> Test Plan found. Preparing to listen on MQTT topic: '{topic_to_verify}'")

        verifier_code = f"""
    # verifier_script.py
    import paho.mqtt.client as mqtt
    import json, time, sys

    MQTT_BROKER = "{local_pc_ip}"
    MQTT_PORT = 1883
    TEST_PLAN = {json.dumps(test_plan)}
    DEVICE_ID = "{device_id}"
    # BUGFIX: The topic is now correctly formatted before being embedded in the script
    TOPIC_TO_VERIFY = "{topic_to_verify}"

    test_results = {{}}
    current_step_index = 0
    start_time = time.time()
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)

    def on_connect(client, userdata, flags, reason_code, properties):
        print(f"Verifier: Connected to MQTT Broker with reason code {{reason_code}}.")
        if reason_code == 0:
            # 核心修正：订阅正确的数据主题
            print(f"Verifier: Subscribing to topic: {{TOPIC_TO_VERIFY}}")
            client.subscribe(TOPIC_TO_VERIFY)
        else:
            print("Verifier: MQTT connection failed, exiting.")
            sys.exit(1)

    def on_message(client, userdata, msg):
        global current_step_index, start_time
        payload = msg.payload.decode('utf-8').strip()
        print(f"Verifier: Received message on '{{msg.topic}}': '{{payload}}'")

        if current_step_index >= len(TEST_PLAN['sequence']): return
        step = TEST_PLAN['sequence'][current_step_index]

        if step['expected_log_contains'] in payload:
            print(f"  -> MATCH FOUND for step '{{step['name']}}'!")
            test_results[step['name']] = "PASS"
            current_step_index += 1
            start_time = time.time()

    client.on_connect = on_connect
    client.on_message = on_message

    print(f"Verifier: Connecting to {{MQTT_BROKER}}:{{MQTT_PORT}}...")
    client.connect(MQTT_BROKER, MQTT_PORT, 60)
    client.loop_start()

    while current_step_index < len(TEST_PLAN['sequence']):
        step = TEST_PLAN['sequence'][current_step_index]
        timeout = step['timeout_seconds']
        print(f"Verifier: Waiting for message containing '{{step['expected_log_contains']}}'. Timeout in {{timeout - (time.time() - start_time):.1f}}s")
        if time.time() - start_time > timeout:
            print(f"Verifier: TIMEOUT waiting for step '{{step['name']}}'.")
            test_results[step['name']] = "FAIL: Timeout"
            break
        time.sleep(1)

    client.loop_stop()
    client.disconnect()
    print("Verifier: Disconnected from MQTT.")

    all_passed = all(res == "PASS" for res in test_results.values()) and len(test_results) == len(TEST_PLAN['sequence'])
    final_result = {{"status": "PASS" if all_passed else "FAIL", "details": test_results}}

    print(f"Verifier: Final Result -> {{json.dumps(final_result)}}")
    with open("test_result.json", "w") as f: json.dump(final_result, f)

    if not all_passed:
        sys.exit(1)
    """
        verifier_script_path = build_dir / "run_verification.py"
        verifier_script_path.write_text(verifier_code, encoding="utf-8")

        # 注意：此节点不再返回 build_dir，因为它只是使用它，而不是创建它
        return {}


    def compile_node(state: AgentState) -> Dict:
        # 导入智能日志函数
        from app.services.workflow_service import _log_with_personality, analyze_compilation_error

        workflow_id = state.get('workflow_id')
        build_dir_path = state.get("build_dir")

        if not build_dir_path:
            _log_with_personality(workflow_id, "找不到构建目录，工作区准备可能失败了", "error")
            return {"feedback": "FAIL: No build directory available - workspace preparation may have failed"}

        build_dir = Path(build_dir_path)

        current_device_task = state.get('current_device_task')
        if not current_device_task:
            _log_with_personality(workflow_id, "找不到当前设备任务信息", "error")
            return {"feedback": "FAIL: No current device task available"}

        device_id = current_device_task['internal_device_id']
        device_role = current_device_task.get('device_role', device_id)

        # 使用辅助函数生成与 integrator_node 一致的环境名
        folder_name, env_name = generate_short_names(device_id, device_role)

        _log_with_personality(workflow_id, f"正在编译{device_role}的固件...检查代码语法和依赖关系", "compiling")
        print(f"\n--- [COMPILE NODE] PHASE 1/3: Compiling firmware for {device_id} (env: {env_name}) in '{build_dir}' ---")

        # 【诊断代码】打印关键环境变量和 pio 系统信息
        print("--- [DIAGNOSIS] Checking subprocess environment ---")
        try:
            home_dir = os.path.expanduser("~")
            print(f"  - Python's view of HOME: {home_dir}")
            print(f"  - Env Var 'HOME': {os.environ.get('HOME')}")
            print(f"  - Env Var 'USERPROFILE' (Windows): {os.environ.get('USERPROFILE')}")
            print(f"  - Env Var 'PLATFORMIO_HOME_DIR': {os.environ.get('PLATFORMIO_HOME_DIR')}")

            # 运行 pio system info 来查看 PlatformIO 是如何看待自己的环境的
            pio_info_proc = subprocess.run(
                ["platformio", "system", "info"],
                capture_output=True, text=True, encoding='utf-8', errors='ignore'
            )
            print("\n  --- pio system info output ---")
            print(pio_info_proc.stdout)
            print("  ------------------------------\n")

        except Exception as diag_e:
            print(f"  - Diagnosis step failed: {diag_e}")

        # 【诊断代码结束】
        # 性能优化：不再将所有输出捕获到内存，而是直接流式传输到控制台
        try:
            command = ["platformio", "run"]
            print(f"  -> Executing command: {' '.join(command)}")
            process = subprocess.Popen(
                command,
                cwd=build_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT, # 合并标准错误和标准输出
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            full_log = ""
            # 实时读取和打印输出
            for line in iter(process.stdout.readline, ''):
                print(line, end='') # 实时打印到控制台
                full_log += line
            process.stdout.close()
            return_code = process.wait() # 等待进程结束

            if return_code != 0:
                _log_with_personality(workflow_id, "编译失败了！让我看看哪里出了问题...", "error", delay=0.5)

                # 使用智能错误分析
                error_analysis = analyze_compilation_error(full_log)

                # 显示错误定位信息
                if error_analysis["line_number"]:
                    _log_with_personality(workflow_id,
                        f"我发现错误出现在 {error_analysis['file_location']} 第{error_analysis['line_number']}行：{error_analysis['error_message']}",
                        "analyzing", delay=1.0)

                # 显示AI的思考过程
                _log_with_personality(workflow_id, error_analysis["ai_thought"], "thinking", delay=1.0)

                print(f"--- [COMPILE NODE] COMPILE FAILED with return code {return_code} ---")
                # 为修复流程返回带有 FAIL 前缀的完整日志
                return {"feedback": f"FAIL: Compile process failed.\n{full_log}", "error_analysis": error_analysis}

        except Exception as e:
            error_msg = f"FAIL: An unexpected error occurred during compilation. Exception: {type(e).__name__}: {str(e)}"
            _log_with_personality(workflow_id, f"编译过程中发生了意外错误：{str(e)}", "error")
            print(f"--- [COMPILE NODE] {error_msg} ---")
            return {"feedback": error_msg}

        # 编译成功后的逻辑 - 使用新的环境名和构建目录
        firmware_path = build_dir / ".bld" / env_name / "firmware.bin"
        if not firmware_path.exists():
            _log_with_personality(workflow_id, "编译完成但找不到固件文件，可能有问题", "warning")
            return {"feedback": f"FAIL: Compiled firmware.bin not found at {firmware_path}"}

        _log_with_personality(workflow_id, "太好了！编译成功，固件已准备就绪", "success")
        print(f"--- [COMPILE NODE] COMPILATION SUCCESS. Firmware ready at: {firmware_path.resolve()} ---")
        return {
            "feedback": "PASS: Compilation successful.",
            "firmware_path": str(firmware_path),
            "build_dir": str(build_dir)
        }

    def usb_upload_node(state: AgentState) -> Dict:
        build_dir = Path(state["build_dir"])
        current_device_task = state.get('current_device_task')
        if not current_device_task:
            return {"feedback": "FAIL: No current device task found"}

        device_id = current_device_task['internal_device_id']
        print(f"\n--- [USB UPLOAD NODE] PHASE 2/3: Uploading firmware via USB for {device_id} ---")

        try:
            command = ["platformio", "run", "--target", "upload"]
            print(f"  -> Executing in '{build_dir}': {' '.join(command)}")
            # 同样使用流式输出来提供实时反馈
            process = subprocess.Popen(
                command,
                cwd=build_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True, encoding='utf-8', errors='ignore'
            )
            full_log = ""
            for line in iter(process.stdout.readline, ''):
                print(line, end='')
                full_log += line
            process.stdout.close()
            return_code = process.wait(timeout=300)

            if return_code != 0:
                print(f"--- [USB UPLOAD NODE] UPLOAD FAILED with return code {return_code} ---")
                return {"feedback": f"FAIL: USB Upload process failed.\n\n{full_log}"}

            print("--- [USB UPLOAD NODE] USB UPLOAD COMMAND EXECUTED ---")
            # 即使命令成功，也给予短暂延时，确保设备重启和网络连接
            print("  -> Waiting 10 seconds for device to reboot and connect to network...")
            time.sleep(10)
            return {"feedback": "PASS: USB upload command executed."}

        except subprocess.TimeoutExpired as e:
            msg = f"FAIL: USB Upload process timed out after {e.timeout} seconds."
            print(f"--- [USB UPLOAD NODE] {msg} ---")
            return {"feedback": msg}
        except Exception as e:
            msg = f"FAIL: Unexpected error during USB upload. Exception: {type(e).__name__}: {str(e)}"
            print(f"--- [USB UPLOAD NODE] {msg} ---")
            return {"feedback": msg}

    def pre_deployment_pause_node(state: AgentState) -> Dict:
        print("\\n--- Waiting for user to select deployment method... ---")
        return {"available_actions": ["DEPLOY_USB", "DEPLOY_OTA"]}

    def ota_deployment_node(state: AgentState) -> Dict:
        print("\\n--- Entering Node: Real OTA Deployment ---")
        build_dir = Path(state["build_dir"])
        firmware_path = Path(state["firmware_path"])
        device_id = state['current_device_task']['internal_device_id']

        # 新增编译步骤
        print(f"  -> Compiling firmware for {device_id} before OTA deployment")
        try:
            compile_cmd = ["platformio", "run"]
            compile_process = subprocess.Popen(
                compile_cmd,
                cwd=build_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True, encoding='utf-8', errors='ignore'
            )
            full_log = ""
            for line in iter(compile_process.stdout.readline, ''):
                print(line, end='')
                full_log += line
            compile_process.stdout.close()
            return_code = compile_process.wait(timeout=300)

            if return_code != 0:
                return {"feedback": f"FAIL: Compilation failed before OTA. Error:\n{full_log}"}

        except Exception as e:
            return {"feedback": f"FAIL: Compilation error: {type(e).__name__}: {str(e)}"}

        # 原部署流程
        local_pc_ip = get_local_ip()
        ota_pusher_code = textwrap.dedent(f"""
        import paho.mqtt.client as mqtt
        import json, time
        MQTT_BROKER = "{local_pc_ip}"
        MQTT_PORT = 1883
        DEVICE_ID = "{device_id}"
        client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        time.sleep(1)
        if client.is_connected():
            command = {{"action": "update", "file": "firmware.bin"}}
            topic = f"/ota/{{DEVICE_ID}}/command"
            client.publish(topic, json.dumps(command))
        else:
            exit(1)
        time.sleep(1)
        client.loop_stop()
        client.disconnect()
        """)
        ota_pusher_script_path = build_dir / "ota_pusher.py"
        ota_pusher_script_path.write_text(ota_pusher_code, encoding="utf-8")
        http_server_dir = firmware_path.parent
        http_server_process = subprocess.Popen(["python", "-m", "http.server", "8000"], cwd=http_server_dir,
                                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        try:
            subprocess.run(["python", ota_pusher_script_path.name], cwd=build_dir, check=True, capture_output=True,
                        text=True, encoding='utf-8', errors='ignore')
            time.sleep(20)
        except subprocess.CalledProcessError as e:
            return {"feedback": f"FAIL: OTA push script failed. Error: {e.stderr}"}
        finally:
            http_server_process.terminate()
        return {"feedback": "PASS: Real OTA deployment command sent."}


    def deploy_and_verify_node(state: AgentState) -> Dict:
        print(f"\n--- [VERIFICATION NODE] PHASE 3/3: Verifying device operation ---")
        test_plan = state.get('test_plan')

        if not test_plan or not isinstance(test_plan, dict) or not test_plan.get("sequence"):
            print("  -> Verification skipped (no test plan or sequence found).")
            return {"feedback": "PASS: Verification skipped (no test plan generated)."}

        device_id = state['current_device_task']['internal_device_id']
        build_dir = Path(state["build_dir"])
        local_pc_ip = get_local_ip()

        # 核心修正：从 test_plan 动态获取要验证的 MQTT 主题
        raw_topic = test_plan.get("device_log_topic", f"/debug/{device_id}/log")
        # 确保 topic 中的占位符被正确替换（支持两种格式：{DEVICE_ID} 和 DEVICE_ID）
        topic_to_verify = raw_topic.format(DEVICE_ID=device_id)  # 处理 {DEVICE_ID} 格式
        topic_to_verify = topic_to_verify.replace("DEVICE_ID", device_id)  # 处理 DEVICE_ID 格式
        print(f"  -> Test Plan found. Preparing to listen on MQTT topic: '{topic_to_verify}'")

        verifier_code = f"""
    # verifier_script.py
    import paho.mqtt.client as mqtt
    import json, time, sys

    MQTT_BROKER = "{local_pc_ip}"
    MQTT_PORT = 1883
    TEST_PLAN = {json.dumps(test_plan)}
    DEVICE_ID = "{device_id}"
    # BUGFIX: The topic is now correctly formatted before being embedded in the script
    TOPIC_TO_VERIFY = "{topic_to_verify}" 

    test_results = {{}}
    current_step_index = 0
    start_time = time.time()
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)

    def on_connect(client, userdata, flags, reason_code, properties):
        print(f"Verifier: Connected to MQTT Broker with reason code {{reason_code}}.")
        if reason_code == 0:
            # 核心修正：订阅正确的数据主题
            print(f"Verifier: Subscribing to topic: {{TOPIC_TO_VERIFY}}")
            client.subscribe(TOPIC_TO_VERIFY)
        else:
            print("Verifier: MQTT connection failed, exiting.")
            sys.exit(1)

    def on_message(client, userdata, msg):
        global current_step_index, start_time
        payload = msg.payload.decode('utf-8').strip()
        print(f"Verifier: Received message on '{{msg.topic}}': '{{payload}}'")

        if current_step_index >= len(TEST_PLAN['sequence']): return
        step = TEST_PLAN['sequence'][current_step_index]

        if step['expected_log_contains'] in payload:
            print(f"  -> MATCH FOUND for step '{{step['name']}}'!")
            test_results[step['name']] = "PASS"
            current_step_index += 1
            start_time = time.time()

    client.on_connect = on_connect
    client.on_message = on_message

    print(f"Verifier: Connecting to {{MQTT_BROKER}}:{{MQTT_PORT}}...")
    client.connect(MQTT_BROKER, MQTT_PORT, 60)
    client.loop_start()

    while current_step_index < len(TEST_PLAN['sequence']):
        step = TEST_PLAN['sequence'][current_step_index]
        timeout = step['timeout_seconds']
        print(f"Verifier: Waiting for message containing '{{step['expected_log_contains']}}'. Timeout in {{timeout - (time.time() - start_time):.1f}}s")
        if time.time() - start_time > timeout:
            print(f"Verifier: TIMEOUT waiting for step '{{step['name']}}'.")
            test_results[step['name']] = "FAIL: Timeout"
            break
        time.sleep(1)

    client.loop_stop()
    client.disconnect()
    print("Verifier: Disconnected from MQTT.")

    all_passed = all(res == "PASS" for res in test_results.values()) and len(test_results) == len(TEST_PLAN['sequence'])
    final_result = {{"status": "PASS" if all_passed else "FAIL", "details": test_results}}

    print(f"Verifier: Final Result -> {{json.dumps(final_result)}}")
    with open("test_result.json", "w") as f: json.dump(final_result, f)

    if not all_passed:
        sys.exit(1)
    """
        verifier_script_path = build_dir / "run_verification.py"
        verifier_script_path.write_text(verifier_code, encoding="utf-8")

        print(f"  -> Verification script created at '{verifier_script_path}'. Executing...")
        try:
            process = subprocess.run(
                ["python", "run_verification.py"],
                cwd=build_dir,
                check=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            print("--- Verifier Script Output ---")
            print(process.stdout)
            print("----------------------------")

            with open(build_dir / "test_result.json", "r") as f:
                test_output = json.load(f)
            if test_output["status"] == "PASS":
                print("--- [VERIFICATION NODE] VERIFICATION SUCCESS ---")
                return {"feedback": "PASS: All hardware-in-the-loop tests passed."}
            else:
                details = json.dumps(test_output['details'])
                print(f"--- [VERIFICATION NODE] VERIFICATION FAILED. Details: {details} ---")
                return {"feedback": f"FAIL: Verification failed. Details: {details}"}

        except subprocess.CalledProcessError as e:
            details = f"Verification script exited with error code {e.returncode}."
            print(f"--- [VERIFICATION NODE] VERIFICATION FAILED. {details} ---")
            print("--- Verifier Script Error Output ---")
            print(e.stdout)
            print("----------------------------------")
            # 【契约驱动生成】统一失败文案，确保路由正确识别
            return {"feedback": f"FAIL: Verification failed. {details}\n{e.stdout}"}
    # 【路由修正】check_unit_test_result 保持不变，但它的调用位置和上下文变了
    def check_unit_test_result(state: AgentState) -> str:
        """决策函数：检查编译或验证结果，决定是继续还是修复。"""
        if "FAIL" in state.get('feedback', ''):
            print(f"--- [ROUTING] Feedback indicates FAILURE. Routing to REPAIR. ---")
            return "REPAIR"
        print(f"--- [ROUTING] Feedback indicates PASS. Routing to next step. ---")
        return "PASS"

    # 【路由修正】deploy_and_verify_node 之后的路由
    def route_after_verification(state: AgentState) -> str:
        """
        一个专门用于验证节点之后的新路由函数。
        它将区分可修复的编译失败和不可修复的验证失败。
        """
        feedback = state.get('feedback', '')
        if "FAIL: Verification failed" in feedback:
            print(
                "--- [ROUTING] Verification failed. This is a non-recoverable runtime error for the current device. Ending this device's workflow.")
            return "FINISH_DEVICE"  # 新的路由目标
        elif "FAIL" in feedback:
            # 其他类型的失败（理论上不应该在这里发生，但作为保障）
            print(f"--- [ROUTING] An unexpected failure occurred. Routing to REPAIR.")
            return "REPAIR"

        print(f"--- [ROUTING] Verification successful. Routing to DP Extractor.")
        return "PASS"


    # 文件: app/langgraph_def/graph_builder.py

    def dp_designer_node(state: AgentState) -> Dict:
        """
        [V2.0 CONTRACT-FIRST] 新的DP设计节点 (由原dp_extractor_node改造)
        在开发流程开始时，根据设备的高级描述，主动设计其功能点(DP)契约。
        """
        print("\\n--- [CONTRACT DESIGN]: Designing Data Point contract for current device ---")

        current_device_task = state.get("current_device_task")
        if not current_device_task:
            return {"device_dp_contract": []}

        device_role = current_device_task.get("device_role", "Unknown Device")
        device_description = current_device_task.get("description", "")
        print(f"  -> Designing DPs for device role: '{device_role}'")

        # 复用高质量的Prompt结构，但将任务从"提取"改为"设计"

        prompt_template = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(textwrap.dedent("""
                你是一位顶级的物联网解决方案架构师。你的任务是根据设备的高级功能描述，为其设计一套完整且符合涂鸦规范的功能点（Data Point, DP）。

                **设计准则：**
                1.  **全面性**: 你必须为描述中的每一个独立功能（如温度、湿度、开关、亮度调节）都设计一个对应的DP。
                2.  **标准化**: 严格遵循涂鸦DP命名和类型规范。`code`使用下划线小写，`name`使用易懂的中文。
                3.  **模式判断**: 根据功能描述判断数据流向。例如，"读取并上报光照"是`ro` (只上报)；"控制开关"是`rw` (可上报可下发)。
                4.  **类型选择**: 精确选择数据类型。整数用`value`，小数用`float`，开关用`bool`。
                5.  **合理默认值**: 为数值型DP提供合理的范围、步长和倍数。例如，传感器上报的倍数通常为`0`，步长为`1`。
                6.  **ID生成**: `id`字段从101开始，依次递增。

                功能点信息dp_info_list的格式是一个JSON列表，每个元素都是一个字典，代表一个功能点。
                请参考以下示例结构：
                ```json
                [
                    {{
                        "id": 104,
                        "name": "亮度",
                        "code": "bright_value",
                        "mode": "rw",
                        "type": "value",
                        "define": "",
                        "remark": "",
                        "range_min": "10",
                        "range_max": "1000",
                        "step": "1",
                        "multiple": "0",
                        "unit": ""
                    }}
                ]
                ```

                请严格遵循以下功能点信息规则：
                1. id：功能点ID，必填，整数，范围在101-499之间。从101开始递增生成，并避免重复。
                2. name：功能点名称，必填，根据功能描述生成，支持中文。
                3. code：标识符，必填，支持英文，通常是功能的小写下划线形式。
                4. mode：数据传输类型，必填。 "rw" (可上报可下发), "ro" (只上报), "wr" (只下发)。
                5. type：数据类型，必填。 "value", "string", "data", "bool", "enum"。
                6. define：数据定义。字符型填写最大长度；枚举型填写枚举值并用 "," 隔开；其他类型留空。
                7. remark：备注，默认留空。
                8. range_min/range_max/step/multiple：仅数值型必填，若无则给合理默认值。
                9. unit：单位，一般留空。

                你的输出必须是一个只包含JSON列表的字符串，不需要任何额外的解释或文本。如果设备没有任何需要与涂鸦云交互的功能，请返回一个空的JSON列表 `[]`。
                """)),
            HumanMessagePromptTemplate.from_template("请为以下设备设计功能点(DP)列表：\n- **设备角色**: {device_role}\n- **功能描述**: {device_description}")
        ])

        chain = prompt_template | dp_designer_model | JsonOutputParser()

        try:
            designed_dp_list = chain.invoke({
                "device_role": device_role,
                "device_description": device_description
            })

            final_list = []
            next_id = 101
            if isinstance(designed_dp_list, list):
                for i, dp in enumerate(designed_dp_list):
                    if isinstance(dp, dict) and "code" in dp and "name" in dp:
                        dp['id'] = next_id + i
                        for key in ["mode", "type", "define", "remark", "range_min", "range_max", "step", "multiple", "unit"]:
                            if key not in dp:
                                dp[key] = ""
                        final_list.append(dp)

            print(f"  -> Successfully designed {len(final_list)} DP(s) for '{device_role}':")
            print(json.dumps(final_list, indent=2, ensure_ascii=False))

            # 将设计好的DP契约存入一个新的状态键
            result = {"device_dp_contract": final_list}

            # 【核心修复】将DP信息合并到统一通信契约中，建立完整的数据契约
            if 'unified_communication_contract' in state:
                unified_contract = state['unified_communication_contract'].copy()

                # 如果还没有schema字段，创建它
                if 'schema' not in unified_contract:
                    unified_contract['schema'] = {}

                # 为当前设备的发布主题添加payload schema
                topic_map = unified_contract.get('topic_map', {})
                device_topics = topic_map.get(device_role, {})

                if device_topics.get('pub'):  # 如果这个设备有发布主题
                    for pub_topic in device_topics['pub']:
                        # 构建payload schema
                        payload_schema = {}
                        for dp in final_list:
                            if dp.get('mode') in ['ro', 'rw']:  # 只有可上报的DP才会出现在payload中
                                payload_schema[dp['code']] = dp['type']

                        # 将schema信息存储到统一契约中
                        unified_contract['schema'][pub_topic] = {
                            "publisher": device_role,
                            "payload_schema": payload_schema
                        }

                        print(f"  -> 🔧 Added payload schema for topic '{pub_topic}':")
                        print(f"     Publisher: {device_role}")
                        print(f"     Schema: {payload_schema}")

                result['unified_communication_contract'] = unified_contract
                print(f"  -> ✅ Enhanced unified_communication_contract with DP schema")
            else:
                print(f"  -> ⚠️ WARNING: unified_communication_contract not found in state")

            return result
        except Exception as e:
            print(f"  -> ERROR: Failed to design DPs: {e}")
            return {"device_dp_contract": []}


    def normalize_numeric_dp(dp: dict) -> dict:
        """
        让数值型DP符合Tuya模板约束：
        - value类型的step和multiple必须是正整数
        - 如果需要小数，自动转换为float类型
        """
        import re

        dp_type = dp.get("type", "").lower()
        step = str(dp.get("step", "")).strip()
        multiple = str(dp.get("multiple", "")).strip()

        # value型必须是整数
        if dp_type == "value":
            # 检查是否有小数，如果有则转为float类型
            if ("." in step and step != "") or ("." in multiple and multiple != ""):
                print(f"  -> 检测到value类型DP '{dp.get('name', 'Unknown')}' 含有小数间距/倍数，自动转换为float类型")
                dp["type"] = "float"
            else:
                # 确保step和multiple是正整数
                for k in ("range_min", "range_max", "step", "multiple"):
                    v = str(dp.get(k, "")).strip()
                    if v and re.match(r"^\d+(\.\d+)?$", v):  # 合法数字
                        int_v = int(float(v))
                        dp[k] = str(max(int_v, 1)) if k == "step" else str(int_v)
                    else:
                        dp[k] = "1" if k == "step" else "0"

        return dp


    def device_artifact_generator_node(state: AgentState) -> Dict:
        """
        在单个设备处理流程结束后，为其生成专属的、符合涂鸦导入规范的产出物 (.xlsx 文件)。
        [V3.1 修正版]:
        1. 修正了工作表(Sheet)的名称为"基础数据类型"，以匹配模板要求。
        2. 增加了对数值型(value)功能点的默认值处理逻辑，确保"间距"等必填数字段位不为空，修复"数据属性值错误"。
        3. 新增数据规范化处理，确保value类型的step/multiple为整数，避免Tuya平台校验错误。
        """
        print("\\n--- [DEVICE ARTIFACT GENERATOR V3.1]: Generating artifacts for current device ---")

        workspace_path = Path(state['workspace_path'])
        current_device = state.get('current_device_task', {})
        device_role = current_device.get('device_role', 'unknown_device')

        # V2.0 CONTRACT-FIRST: 使用契约数据而不是提取的数据
        dp_list = state.get('device_dp_contract', [])

        if not dp_list:
            print(f"  -> No data points designed for device '{device_role}'. Skipping Excel file generation.")
            return {"device_dp_contract": []}

        safe_device_role = "".join(c for c in device_role if c.isalnum() or c in (' ', '_')).rstrip()
        filename = workspace_path / f"{safe_device_role}_dps.xlsx"
        print(f"  -> Generating Tuya DP template for '{device_role}' at: {filename}")

        try:
            wb = Workbook()
            ws = wb.active
            # =================================================================================
            # 核心修改 1: 将表格(Sheet)名称修改为 "基础数据类型"
            # =================================================================================
            ws.title = "基础数据类型"

            # 第 1 行：说明内容 (与上一版相同)
            instruction_text = (
                "说明内容（勿删）\n"
                "此导入模板支持导入功能类型为属性，数据类型为：数值型、字符型、时间型、布尔型、枚举型、透传型、故障型的自定义功能点"
            )
            ws.append([instruction_text] + [""] * 11)

            # 第 2 行：各列的填写规则说明 (与上一版相同)
            rules_row = [
                "必填\n请输入数字，仅支持101-499",
                "必填\n支持中文、大小写字母、数字、短划线、下划线、斜杠和小数点，必须以中文、英文或数字开头",
                "必填\n支持大小写字母、数字和下划线",
                "必填\n单选\n可下发可上报，填写rw\n只上报，填写ro\n只下发，填写wr",
                "必填\n单选\n数值型，填写value\n字符型，填写string\n时间型，填写date\n布尔型，填写bool\n枚举型，填写enum\n透传型，填写raw\n故障型，填写fault\n单精度浮点型，填写float\n双精度浮点型，填写double\n",
                "数值型，不填\n字符型，必填，填写最大长度数值，0-1024\n时间型，不填\n布尔型，不填\n枚举型，必填，填写枚举值，并用英文逗号隔开\n透传型，不填\n故障型，必填，填写故障值，并用英文逗号隔开\n单精度浮点型，不填\n双精度浮点型，不填",
                "非必填\n中英文字符",
                "仅数值型、单精度浮点型、双精度浮点型、长整型必填\n\n最小值<最大值\n数值根据类型，填写\n数值型，请填写正整数\n单精度浮点型，请填写精度小数\n双精度浮点型，请填写精度小数",
                "仅数值型、单精度浮点型、双精度浮点型、长整型必填\n\n最大值>最小值\n数值根据类型，填写\n数值型，请填写正整数\n单精度浮点型，请填写精度小数\n双精度浮点型，请填写精度小数",
                "仅数值型、单精度浮点型、双精度浮点型必填",
                "仅数值型必填",
                "非必填\n仅数值型、单精度浮点型、双精度浮点型填写"
            ]
            ws.append(rules_row)
            ws.row_dimensions[1].height = 60
            ws.row_dimensions[2].height = 250

            # 第 3 行：数据表头 (与上一版相同)
            headers = [
                "DP ID", "功能点名称", "标识符", "数据传输类型", "数据类型",
                "数据定义", "备注", "数据范围-最小值", "数据范围-最大值",
                "间距", "倍数", "单位"
            ]
            ws.append(headers)

            column_widths = [15, 25, 25, 15, 15, 40, 30, 15, 15, 10, 10, 20]
            for i, column_width in enumerate(column_widths, 1):
                ws.column_dimensions[get_column_letter(i)].width = column_width

            # 从第 4 行开始写入动态数据
            for raw_dp in dp_list:
                # =================================================================================
                # 核心修改 2: 数据规范化处理，确保符合Tuya模板约束
                # =================================================================================
                dp = normalize_numeric_dp(raw_dp)  # 应用数据规范化

                # =================================================================================
                # 核心修改 3: 对数值型(value)功能点，提供关键字段的默认值，防止因空值导致导入失败
                # =================================================================================
                dp_type = dp.get("type")
                is_numeric = dp_type in ["value", "float", "double"]

                # 根据模板规则，"间距" 和 "倍数" 等字段在数值类型下是必填的。
                # 此前默认为空字符串""，导致了 "数据属性值错误"。
                # 现在我们为这些字段提供合理的数字默认值。
                range_min = dp.get("range_min") or ("0" if is_numeric else "")
                range_max = dp.get("range_max") or ("1000" if is_numeric else "")
                step = dp.get("step") or ("1" if is_numeric else "")
                multiple = dp.get("multiple") or ("0" if is_numeric else "")

                row_data = [
                    dp.get("id", ""),
                    dp.get("name", ""),
                    dp.get("code", ""),
                    dp.get("mode", ""),
                    dp_type or "",
                    dp.get("define", ""),
                    dp.get("remark", ""),
                    range_min,
                    range_max,
                    step,
                    multiple,
                    dp.get("unit", "")
                ]
                ws.append(row_data)

            wb.save(filename)
            print(f"  -> Successfully saved Excel file for '{device_role}' with {len(dp_list)} data points.")

        except Exception as e:
            print(f"  -> ERROR: Failed to generate Excel file for '{device_role}': {e}")

        # V2.0 CONTRACT-FIRST: 保持契约状态，为下一个设备做准备
        result = {"device_dp_contract": dp_list}
        if 'unified_communication_contract' in state:
            result['unified_communication_contract'] = state['unified_communication_contract']
        
        return result

    # =================================================================================
    # 5. Graph Definition & Logic
    # =================================================================================

    def check_device_queue(state: AgentState) -> str:
        """【修正】决策函数：检查是否还有待处理的设备任务。"""
        # 在开始处理一个新设备前，清理上个设备可能留下的旧状态
        state['feedback'] = ""
        state['user_action'] = None
        state['deployment_choice'] = None
        if state.get("current_device_task"):
            return "continue_to_development"
        return "finish_all_devices"

    def check_module_queue(state: AgentState) -> str:
        """决策函数：检查是否还有待处理的模块任务。"""
        if state.get("current_module_task"):
            return "continue_development"
        return "finish_development"

    def check_unit_test_result(state: AgentState) -> str:
        """决策函数：检查编译或验证结果，决定是继续还是修复。"""
        if "FAIL" in state.get('feedback', ''):
            print(f"--- [ROUTING] Feedback indicates FAILURE. Routing to REPAIR. ---")
            return "REPAIR"
        print(f"--- [ROUTING] Feedback indicates PASS. Routing to next step. ---")
        return "PASS"

    def master_router_node(state: AgentState) -> dict:
        """【修正】图的总入口节点，仅作为正式的节点存在，返回空字典。"""
        # 导入智能日志函数
        from app.services.workflow_service import _log_with_personality

        workflow_id = state.get('workflow_id')
        project_name = state.get('project_name', '项目')

        _log_with_personality(workflow_id, f"让我先分析一下您的需求...您想要开发 {project_name}，我来仔细研究一下具体要求", "thinking", delay=1.0)
        print("--- [MASTER ROUTER] Evaluating entry point... ---")
        return {}

    def master_router_logic(state: AgentState) -> str:
        """【修正】用于总入口节点的路由决策函数。"""
        user_action = state.get("user_action")

        if user_action:
            print(f"--- [ROUTING LOGIC] User action '{user_action}' found. Resuming from pause. ---")
            return "resume_from_pause"
        else:
            print("--- [ROUTING LOGIC] No user action. Starting from beginning. ---")
            return "start_from_beginning"

    # 【新增】一个合格的、用于恢复流程的 “工作” 节点
    def resume_router_node(state: AgentState) -> dict:
        """
        这是一个合格的图节点。它的工作很简单，就是打印一条日志。
        它将在 master_router 决定恢复流程后被调用。
        """
        print("--- [RESUME NODE] Workflow is resuming. Preparing to route deployment action... ---")
        return {}  # 作为一个合格的节点，它返回一个字典


    # 【新增】一个合格的、用于恢复流程的 “决策” 函数
    def route_deployment_logic(state: AgentState) -> str:
        """
        这是一个合格的条件边函数。它检查用户的操作并返回一个字符串决策。
        """
        user_action = state.get("user_action")
        if user_action == 'DEPLOY_USB':
            print("--- [ROUTING LOGIC] User chose USB. Routing to usb_upload_node. ---")
            return "REAL_USB_DEPLOY"
        elif user_action == 'DEPLOY_OTA':
            print("--- [ROUTING LOGIC] User chose OTA. Routing to ota_deployment_node. ---")
            return "REAL_OTA_DEPLOY"

        # 异常情况，理论上不应发生，但作为保护
        print("--- [ROUTING LOGIC] No user action found in a resumed state. Ending deployment phase. ---")
        return "END_DEPLOYMENT"

    def build_graph():
        """
        【V3.2 架构修正】构建支持“暂停-恢复”生命周期的工作流图。
        """
        workflow = StateGraph(AgentState)

        # 添加所有节点
        workflow.add_node("master_router", master_router_node)
        workflow.add_node("plan_enrichment_node", plan_enrichment_node)
        workflow.add_node("device_dispatcher", device_dispatcher_node)
        workflow.add_node("dp_designer", dp_designer_node)  # V2.0 CONTRACT-FIRST: 新的DP设计节点
        workflow.add_node("contract_debugger", contract_debugger_node)  # 新增契约调试器节点
        workflow.add_node("device_artifact_generator", device_artifact_generator_node)
        workflow.add_node("module_architect", module_architect_node)
        workflow.add_node("module_dispatcher", module_dispatcher_node)
        workflow.add_node("api_designer", api_designer_node)
        workflow.add_node("developer", developer_node)
        workflow.add_node("integrator", integrator_node)
        workflow.add_node("prepare_workspace", prepare_workspace_node)
        workflow.add_node("test_plan_designer", test_plan_designer_node)
        workflow.add_node("deployment_and_verification", deployment_and_verification_node)
        workflow.add_node("compile_node", compile_node)
        workflow.add_node("pre_deployment_pause", pre_deployment_pause_node)
        workflow.add_node("usb_upload_node", usb_upload_node)
        workflow.add_node("ota_deployment_node", ota_deployment_node)
        workflow.add_node("deploy_and_verify_node", deploy_and_verify_node)

        # 【新增】添加我们新的、用于恢复的节点
        workflow.add_node("resume_router", resume_router_node)

        # 设定图的唯一入口
        workflow.set_entry_point("master_router")

        # --- 核心路由逻辑 ---

        # 1. 从总入口 master_router 开始决策
        workflow.add_conditional_edges(
            "master_router",
            master_router_logic,
            {
                # 【核心修改】如果是新开始，先走需求细化，再走设备分发
                "start_from_beginning": "plan_enrichment_node",
                "resume_from_pause": "resume_router"
            }
        )

        workflow.add_edge("plan_enrichment_node", "device_dispatcher")

        # 2. 从 resume_router 节点出发，进行部署方式的决策
        workflow.add_conditional_edges(
            "resume_router",
            route_deployment_logic,  # 使用我们新的、合格的决策函数
            {
                "REAL_USB_DEPLOY": "usb_upload_node",
                "REAL_OTA_DEPLOY": "ota_deployment_node",
                "END_DEPLOYMENT": END  # 异常情况则结束
            }
        )

        # --- 其余的图结构保持不变 ---

        # V2.0 CONTRACT-FIRST: 设备处理循环的新流程
        workflow.add_conditional_edges(
            "device_dispatcher",
            check_device_queue,
            {
                # 关键：分发设备后，第一步是进行DP设计
                "continue_to_development": "dp_designer",
                "finish_all_devices": END
            }
        )

        # 设计DP -> 生成Excel文件 -> 再进行模块划分
        workflow.add_edge("dp_designer", "device_artifact_generator")
        workflow.add_edge("device_artifact_generator", "module_architect")

        # 模块开发循环
        workflow.add_edge("module_architect", "module_dispatcher")
        workflow.add_conditional_edges(
            "module_dispatcher",
            check_module_queue,
            {"continue_development": "api_designer", "finish_development": "integrator"}
        )
        workflow.add_edge("api_designer", "contract_debugger")
        workflow.add_edge("contract_debugger", "developer")
        workflow.add_edge("developer", "module_dispatcher")

        # --- 核心修改点 ---
        # 集成 -> 准备工作区 -> 创建测试计划 -> 编译
        workflow.add_edge("integrator", "prepare_workspace")
        workflow.add_edge("prepare_workspace", "test_plan_designer")
        # --- 修改结束 ---
        workflow.add_edge("test_plan_designer", "deployment_and_verification")  # 注意：这里的 deployment_and_verification 仅用于生成验证脚本
        workflow.add_edge("deployment_and_verification", "compile_node")

        # 编译后的路由
        workflow.add_conditional_edges(
            "compile_node",
            check_unit_test_result,
            {
                "PASS": "pre_deployment_pause",
                "REPAIR": "developer"
            }
        )

        # 暂停节点是此阶段的终点
        workflow.add_edge("pre_deployment_pause", END)

        # 部署后的流程
        workflow.add_edge("usb_upload_node", "deploy_and_verify_node")
        workflow.add_edge("ota_deployment_node", "deploy_and_verify_node")

        # V2.0 CONTRACT-FIRST: 验证后的新路由逻辑
        workflow.add_conditional_edges(
            "deploy_and_verify_node",
            route_after_verification,
            {
                # 验证成功后，无需再进行任何DP操作，直接处理下一个设备
                "PASS": "device_dispatcher",
                "REPAIR": "developer",
                "FINISH_DEVICE": "device_dispatcher"
            }
        )

        # 编译并返回图
        compiled_graph = workflow.compile()
        return compiled_graph