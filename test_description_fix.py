#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备描述传递修复的脚本
"""

def test_module_architect_fix():
    """测试module_architect_node中的描述修复"""
    
    # 模拟设备任务
    device_task = {
        'internal_device_id': 'test-device-123',
        'device_role': '报警器',
        'board': 'ESP32',
        'description': '接收来自大绿板的光照强度和距离数据，如果光照强度高于50lux且距离小于50cm，就让5号引脚上的有源低电平触发蜂鸣器鸣叫，并向涂鸦云发送报警信息。',
        'peripherals': [
            {
                'name': '有源蜂鸣器',
                'model': 'Active Buzzer',
                'pins': [{'name': 'CONTROL_PIN', 'number': 5}],
                'function': 'BUZZER',
                'interface': 'DIGITAL'
            }
        ]
    }
    
    # 模拟AI生成的模块计划（原来会丢失描述的情况）
    ai_generated_plan = {
        'modules': [
            {
                'task_id': 'config_manager',
                'task_type': 'driver',
                'peripheral': 'Core',
                'description': 'Manages all network and device configurations.',
                'dependencies': []
            },
            {
                'task_id': 'active_buzzer_driver',
                'task_type': 'driver',
                'peripheral': '有源蜂鸣器',
                'description': 'A driver for the active buzzer.',
                'dependencies': []
            },
            {
                'task_id': 'app_main',
                'task_type': 'application',
                'description': 'The main application logic.',  # 这里是问题所在！
                'dependencies': ['config_manager', 'active_buzzer_driver']
            }
        ]
    }
    
    print("=== 修复前的情况 ===")
    print(f"原始设备描述: {device_task['description']}")
    print(f"AI生成的app_main描述: {ai_generated_plan['modules'][2]['description']}")
    
    # 应用修复逻辑
    modules = ai_generated_plan['modules']
    for module in modules:
        if module.get('task_id') == 'app_main':
            original_description = device_task.get('description', 'The main application logic.')
            module['description'] = original_description
            print(f"\n=== 修复后的情况 ===")
            print(f"修复后的app_main描述: {module['description']}")
            break
    
    # 验证修复效果
    app_main_module = next((m for m in modules if m.get('task_id') == 'app_main'), None)
    if app_main_module:
        fixed_description = app_main_module['description']
        
        # 检查关键信息是否保留
        key_requirements = [
            '光照强度高于50lux',
            '距离小于50cm',
            '且',  # AND条件
            '5号引脚',
            '蜂鸣器',
            '涂鸦云'
        ]
        
        print(f"\n=== 关键信息检查 ===")
        for req in key_requirements:
            if req in fixed_description:
                print(f"✅ 包含: {req}")
            else:
                print(f"❌ 缺失: {req}")
        
        return fixed_description
    
    return None

def test_developer_node_context():
    """测试developer_node中的额外上下文"""
    
    original_device_description = "接收来自大绿板的光照强度和距离数据，如果光照强度高于50lux且距离小于50cm，就让5号引脚上的有源低电平触发蜂鸣器鸣叫，并向涂鸦云发送报警信息。"
    
    # 模拟生成的上下文
    context_template = """
    <OriginalDeviceRequirements>
    CRITICAL: The original device requirements are: "{original_device_description}"
    You MUST implement ALL specific conditions, thresholds, and logic mentioned in these requirements.
    Pay special attention to:
    - Exact threshold values (e.g., "高于50lux", "小于50cm")
    - Logical operators (e.g., "且" means AND, "或" means OR)
    - Trigger conditions and actions
    </OriginalDeviceRequirements>
    """
    
    generated_context = context_template.format(original_device_description=original_device_description)
    
    print("=== Developer Node 额外上下文 ===")
    print(generated_context)
    
    # 分析上下文是否包含关键信息
    critical_info = [
        "高于50lux",
        "小于50cm", 
        "且" means AND",
        "Trigger conditions"
    ]
    
    print("\n=== 上下文信息检查 ===")
    for info in critical_info:
        if info in generated_context:
            print(f"✅ 包含: {info}")
        else:
            print(f"❌ 缺失: {info}")

if __name__ == "__main__":
    print("🔧 测试设备描述传递修复")
    print("=" * 50)
    
    # 测试module_architect修复
    fixed_description = test_module_architect_fix()
    
    print("\n" + "=" * 50)
    
    # 测试developer_node上下文
    test_developer_node_context()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    
    if fixed_description and "高于50lux且距离小于50cm" in fixed_description:
        print("🎯 修复成功：关键业务逻辑已保留")
    else:
        print("⚠️ 需要进一步检查修复效果")
